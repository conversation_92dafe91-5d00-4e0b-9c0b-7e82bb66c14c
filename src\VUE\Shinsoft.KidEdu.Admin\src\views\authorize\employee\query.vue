<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import Epearch from "@iconify-icons/ep/search";
import View from "./dialogs/view.vue";
import Edit from "./dialogs/edit.vue";
import { useI18n } from "vue-i18n";
import { useResizeObserver } from "@vueuse/core";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "employee:query"
});

/**
 * 当前组件ref
 */
const filterRef = ref();
const listContainerRef = ref();
const listRef = ref();
const viewRef = ref();
const editRef = ref();

/**
 * 基本配置定义
 */
const cfg = reactive({
  enums: {
    gender: [],
    employeeStatus: []
  },
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    height: 0,
    defaultSort: { prop: "displayName", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0) + (cfg.btn.delete ? 45 : 0))
    }
  },
  btn: {
    add: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Add"]);
    }),
    edit: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Edit"]);
    }),
    delete: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Delete"]);
    })
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

onMounted(() => {
  //计算列表高度
  useResizeObserver(listContainerRef.value, entries => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    cfg.list.height = height - 190;
  });
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: "",
  genders: [],
  statuses: []
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;

  const initGender = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("Gender").then(enumInfos => {
        cfg.enums.gender = enumInfos;
        resolve();
      });
    });
  };

  const initEmployeeStatus = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("EmployeeStatus").then(enumInfos => {
        cfg.enums.employeeStatus = enumInfos;
        resolve();
      });
    });
  };

  const allInits = [initGender(), initEmployeeStatus()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  authorizeApi
    .QueryEmployee(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filterRef.value.validate((valid, fields) => {
    if (valid) {
      filter.pageIndex = 1;
      getList();
    }
  });
};

/**
 * 新增：点击【新增】按钮事件
 */
const addRow = () => {
  editRef.value?.open();
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editRef.value?.open(row.id);
};

/**
 * 删除：点击列表中【删除】按钮事件
 */
const deleteRow = (row: any) => {
  viewRef.value?.del(row);
};

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-input
                v-model="filter.keywords"
                clearable
                :placeholder="t('filter.keywords')"
                class="filter-item"
                @keyup.enter="query"
              />
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-select
                v-model="filter.genders"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="2"
                :placeholder="tt('Entity.Employee.EnumGender')"
              >
                <el-option
                  v-for="item in cfg.enums.gender"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-select
                v-model="filter.statuses"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
                :placeholder="tt('Entity.Employee.EnumStatus')"
              >
                <el-option
                  v-for="item in cfg.enums.employeeStatus"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon(Epearch)"
              @click="query"
            >
              查询
            </el-button>
          </el-col>
        </el-row>
        <el-row :gutter="cfg.filter.gutter" type="flex" justify="end">
          <el-col :span="24" class="buttonbar">
            <el-button
              v-if="cfg.btn.add"
              class="new"
              :icon="useRenderIcon('ep:document-add')"
              @click="addRow"
            >
              新建
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          :show-overflow-tooltip="true"
          width="60"
          align="center"
        >
          <template v-slot="scope">
            <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed
          sortable="custom"
          prop="loginName"
          :label="tt('Entity.Employee.LoginName')"
          width="150"
        />
        <el-table-column
          fixed
          sortable="custom"
          prop="displayName"
          :label="tt('Entity.Employee.DisplayName')"
          width="180"
        />
        <el-table-column
          sortable="custom"
          prop="jobNo"
          :label="tt('Entity.Employee.JobNo')"
          width="100"
        />
        <el-table-column
          sortable="custom"
          prop="email"
          :label="tt('Entity.Employee.Email')"
          min-width="200"
        />
        <el-table-column
          sortable="custom"
          prop="title"
          :label="tt('Entity.Employee.Title')"
          width="150"
        />
        <el-table-column
          sortable="custom"
          prop="position"
          :label="tt('Entity.Employee.Position')"
          width="150"
        />
        <el-table-column
          sortable="custom"
          sort-by="enumGender"
          prop="enumGenderDesc"
          :label="tt('Entity.Employee.EnumGender')"
          width="120"
          align="center"
        />
        <el-table-column
          sortable="custom"
          sort-by="enumStatus"
          prop="enumStatusDesc"
          :label="tt('Entity.Employee.EnumStatus')"
          width="120"
          align="center"
        />
        <el-table-column
          fixed="right"
          :label="t('list.operate')"
          class-name="operate"
          :width="cfg.list.operate.width"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              class="view"
              size="small"
              :circle="true"
              :title="t('operate.view')"
              :icon="useRenderIcon('ep:document')"
              @click="viewRow(row)"
            />
            <el-button
              v-if="cfg.btn.edit"
              class="edit"
              size="small"
              :circle="true"
              :title="t('operate.edit')"
              :icon="useRenderIcon('ep:edit')"
              @click="editRow(row)"
            />
            <el-button
              v-if="cfg.btn.delete && useUserStoreHook().info?.employeeId != row.id"
              class="delete"
              size="small"
              :circle="true"
              :title="t('operate.delete')"
              :icon="useRenderIcon('ep:delete')"
              @click="deleteRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <View ref="viewRef" @refresh="getList" />
    <Edit ref="editRef" @refresh="getList" />
  </div>
</template>
