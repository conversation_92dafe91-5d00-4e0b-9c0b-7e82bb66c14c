<script setup lang="ts">
import {
  comanyTreeIcon,
  comanyCheckTreeIcon,
  departmentTreeIcon,
  departmentCheckTreeIcon,
  stationTreeIcon,
  stationCheckTreeIcon,
  iconUser
} from "../utils/static";
import { Plus, EditPen, Delete } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:tree"
});

const emit = defineEmits(["nodeClick", "departmentOperation", "stationOperation"]);

const elTreeRef = ref();

/**
 * 点击树节点
 */
const data = defineModel<any>("data");
const addDepartmentTitle = `${t("operate.append")}${tt("Entity.Department._Entity")}`;
const editDepartmentTitle = `${t("operate.edit")}${tt("Entity.Department._Entity")}`;
const deleteDepartmentTitle = `${t("operate.delete")}${tt("Entity.Department._Entity")}`;
const addStationTitle = `${t("operate.append")}${tt("Entity.Station._Entity")}`;
const editStationTitle = `${t("operate.edit")}${tt("Entity.Station._Entity")}`;
const deleteStationTitle = `${t("operate.delete")}${tt("Entity.Station._Entity")}`;
const departmentOrStationName = `${tt("Entity.Department._Entity")}/${tt("Entity.Station.Name")}`;

/**
 * 点击树节点
 */
const handleNodeClick = node => {
  emit("nodeClick", node);
};

/**
 * 添加部门:appendDepartment;
 * 编辑部门:editDepartment;
 * 删除部门:deleteDepartment;
 */
const departmentOperation = (operationType: any, data: any, node: any) => {
  if (!node.isCurrent) {
    handleNodeClick(node);
  }
  emit("departmentOperation", operationType, data);
};

/**
 * 添加岗位:appentStation;
 * 编辑部门:editStation;
 * 删除部门:deleteStation;
 */
const stationOperation = (operationType: any, data: any, node: any) => {
  if (!node.isCurrent) {
    handleNodeClick(node);
  }
  emit("stationOperation", operationType, data);
};

// 树节点鼠标移入移出
const mouseover = data => {
  data.show = true;
};
// 树节点鼠标移入
const mouseleave = data => {
  data.show = false;
};

const filterText = ref<string>("");

watch(filterText, val => {
  elTreeRef.value!.filter(val);
});

const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.name.includes(value);
};
</script>

<template>
  <div>
    <el-input v-model="filterText" :placeholder="departmentOrStationName" />
    <el-tree
      ref="elTreeRef"
      :data="data"
      node-key="level"
      default-expand-all
      :expand-on-click-node="false"
      :filter-node-method="filterNode"
    >
      <template #default="{ node, data }">
        <!-- 第一级默认图标 -->
        <comanyTreeIcon
          v-if="!node.isCurrent && node.level === 1"
          class="icon"
          @click="handleNodeClick(node)"
        />
        <!-- 第一级选中图标 -->
        <comanyCheckTreeIcon
          v-if="node.isCurrent && node.level === 1"
          class="icon"
          @click="handleNodeClick(node)"
        />

        <!-- 部门默认图标 -->
        <departmentTreeIcon
          v-if="
            !node.isCurrent &&
            node.level !== 1 &&
            (data.stationId === undefined || data.stationId === null)
          "
          class="icon"
          @click="handleNodeClick(node)"
        />
        <!-- 部门选中图标 -->
        <departmentCheckTreeIcon
          v-if="
            node.isCurrent &&
            node.level !== 1 &&
            (data.stationId === undefined || data.stationId === null)
          "
          class="icon"
          @click="handleNodeClick(node)"
        />
        <!-- 岗位默认图标 -->
        <stationTreeIcon
          v-if="
            !node.isCurrent &&
            node.level !== 1 &&
            data.stationId !== undefined &&
            data.stationId !== null
          "
          class="icon"
          @click="handleNodeClick(node)"
        />
        <!-- 岗位选中图标 -->
        <stationCheckTreeIcon
          v-if="
            node.isCurrent &&
            node.level !== 1 &&
            data.stationId !== undefined &&
            data.stationId !== null
          "
          class="icon"
          @click="handleNodeClick(node)"
        />
        <div class="treeNode" @mouseover="mouseover(data)" @mouseleave="mouseleave(data)">
          <div class="treeOprate_Left">
            <span class="custom-tree-node">
              <span :class="[node.childNodes.length ? 'bold' : '', node.isCurrent ? 'orange' : '']">
                <a @click="handleNodeClick(node)"> {{ data.name }} </a>
              </span>
            </span>
          </div>
          <div v-if="data.show" class="treeOprate_Right">
            <span>
              <a
                v-if="data.stationId === undefined || data.stationId === null"
                :style="{ marginRight: '0.5rem' }"
                :title="addDepartmentTitle"
                @click="departmentOperation('appendDepartment', data, node)"
              >
                <el-icon size="15" span="24"><Plus /></el-icon>
              </a>
              <a
                v-if="node.level !== 1 && (data.stationId === undefined || data.stationId === null)"
                :style="{ marginRight: '0.5rem' }"
                :title="editDepartmentTitle"
                @click="departmentOperation('editDepartment', data, node)"
              >
                <el-icon size="15" span="24"><EditPen /></el-icon>
              </a>
              <a
                v-if="node.level !== 1 && (data.stationId === undefined || data.stationId === null)"
                :style="{ marginRight: '0.5rem' }"
                :title="deleteDepartmentTitle"
                @click="departmentOperation('deleteDepartment', data, node)"
              >
                <el-icon size="15" span="24"><Delete /></el-icon>
              </a>
              <a
                v-if="node.level !== 1 && (data.stationId === undefined || data.stationId === null)"
                :style="{ marginRight: '0.5rem' }"
                :title="addStationTitle"
                @click="stationOperation('appendStation', data, node)"
              >
                <el-icon size="15" span="24"> <iconUser /></el-icon>
              </a>
              <a
                v-if="node.level !== 1 && data.stationId !== undefined && data.stationId !== null"
                :style="{ marginRight: '0.5rem' }"
                :title="editStationTitle"
                @click="stationOperation('editStation', data, node)"
              >
                <el-icon size="15" span="24"><EditPen /></el-icon>
              </a>
              <a
                v-if="node.level !== 1 && data.stationId !== undefined && data.stationId !== null"
                :style="{ marginRight: '0.5rem' }"
                :title="deleteStationTitle"
                @click="stationOperation('deleteStation', data, node)"
              >
                <el-icon size="15" span="24"><Delete /></el-icon>
              </a>
            </span>
          </div>
        </div>
      </template>
    </el-tree>
  </div>
</template>
<style lang="scss" scoped>
.icon {
  width: 18px;
  height: 18px;
  margin-right: 3px;
}
.treeOprate_Left {
  width: 60;
  float: left;
}
.treeOprate_Right {
  width: 39%;
  text-align: right;
  float: right;
}
.treeNode {
  width: 100%;
}
</style>
