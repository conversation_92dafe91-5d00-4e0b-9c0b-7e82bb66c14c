<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

var dataModel = defineModel<Record<string, any>>("dataModel", {
  get: val => {
    if (!val) {
      return {};
    }
    return val;
  }
});
var moudle = defineModel<Record<string, any>>("moudle", {
  get: val => {
    if (!val) {
      return {};
    }
    // val.showComponent = true; //后期改为计算属性
    return val;
  }
});

var formColSpan = 12;
</script>
<template>
  <div>
    <el-card class="box-card">
      <template v-slot:header>
        <div class="clearfix">
          <div class="card-title">
            <span>{{ moudle.name }}</span>
          </div>
        </div>
      </template>
      <el-form
        ref="formRef"
        :model="dataModel"
        label-position="right"
        label-width="100"
        class="el-dialog-form"
      >
        <el-row type="flex">
          <el-col :span="formColSpan">
            <el-form-item prop="code" :label="tt('Entity.Role.Code')">
              <!-- <span>{{ dataModel.code }}</span> -->
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" :label="tt('Entity.Role.Name')">
              <!-- <span>{{ dataModel.name }}</span> -->
            </el-form-item>
          </el-col>

          <el-col :span="formColSpan">
            <el-form-item prop="enumFlags" :label="tt('Entity.Role.EnumFlags')">
              <!-- <span>{{ dataModel.enumFlagsDesc }}</span> -->
            </el-form-item>
          </el-col>

          <el-col :span="24">
            <el-form-item prop="remark" :label="tt('Entity.Role.Remark')">
              <!-- <span>{{ dataModel.remark }}</span> -->
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
