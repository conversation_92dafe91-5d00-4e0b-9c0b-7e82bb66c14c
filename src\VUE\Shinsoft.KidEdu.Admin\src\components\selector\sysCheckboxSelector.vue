<script setup lang="ts">
defineOptions({
  name: "SysCheckboxSelector"
});

const props = defineProps({
  enums: {
    required: true,
    type: Array<any>
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 可被勾选的 checkbox 的最小数量
  min: {
    type: Number
  },
  // 可被勾选的 checkbox 的最大数量
  max: {
    type: Number
  },
  //是否显示边框
  border: {
    type: Boolean,
    default: false
  },
  //是否禁用选项
  allowDisableOption: {
    type: Boolean,
    default: true
  }
});
const emit = defineEmits(["change"]);

const model = defineModel("modelValue", {
  type: String,
  default: "",
  get: val => {
    let enumValue = "";

    props.enums.forEach(enumInfo => {
      enumValue = enumInfo.value;
    });

    if (state.orgValue !== val || state.orgEnumsValue !== enumValue) {
      state.orgValue = val;
      state.orgEnumsValue = enumValue;
      setValues(val);
    }
    return val;
  }
});

const state = reactive({
  values: [],
  orgValue: "",
  extValue: "",
  orgEnumsValue: ""
});

const setValues = (val: string) => {
  const newValues = [];
  state.extValue = val;

  props.enums.forEach(enumInfo => {
    if (enumInfo.value !== "" && val !== "") {
      newValues.push(enumInfo.value);
      state.extValue = enumInfo.value;
    }
  });

  state.values = newValues;
};

const setModel = (values: Array<String>) => {
  let newValue: string = state.extValue;
  values.forEach((flag: string) => {
    newValue = flag;
  });
  model.value = newValue;
};

const change = newValues => {
  setModel(newValues);
  emit("change", newValues);
};
</script>

<template>
  {{ "系统变量组件" }}
  <el-checkbox-group
    v-model="state.values"
    :disabled="disabled"
    :min="min"
    :max="max"
    @change="change"
  >
    <el-checkbox
      v-for="item in props.enums"
      :key="item"
      :label="item.name"
      :value="item.id"
      :border="border"
      :disabled="props.allowDisableOption && item.disabled"
    />
  </el-checkbox-group>
  <el-input v-show="false" v-model="model" />
</template>
