<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;
var rules = {
  name: [{ required: true, message: tt("Rule.RoleMember.RoleId:Required"), trigger: "blur" }],
  code: [{ required: true, message: tt("Rule.RoleMember.MemberType:Required"), trigger: "blur" }]
};
var dataModel = defineModel<Record<string, any>>("dataModel", {
  get: val => {
    if (!val) {
      return {};
    }
    return val;
  }
});
var moudle = defineModel<Record<string, any>>("moudle", {
  get: val => {
    if (!val) {
      return {};
    }
    val.showComponent = true; //后期改为计算属性
    return val;
  }
});

var customerModel = ref<Record<string, any>>({});
customerModel.value = dataModel.value.customerModel;

var formColSpan = 12;

const customerFormRef = ref();
//表单验证
async function validate() {
  let result = false;
  // console.log(employeeFormRef.value);
  await customerFormRef.value.validate((valid, fields) => {
    if (valid) {
      result = true;
    } else {
      result = false;
    }
  });
  return result;
}
/**
 * 对外开放的公共方法
 */
defineExpose({
  validate
});
</script>
<template>
  <div>
    <el-card class="box-card">
      <template v-slot:header>
        <div class="clearfix">
          <div class="card-title">
            <span>{{ moudle.name }}</span>
            <div>
              <el-link type="primary">示例按钮2</el-link>
            </div>
          </div>
        </div>
      </template>
      <el-form
        ref="customerFormRef"
        :model="customerModel"
        label-position="right"
        label-width="100"
        class="el-dialog-form"
        :rules="rules"
      >
        <el-row type="flex">
          <el-col :span="formColSpan">
            <el-form-item prop="code" :label="tt('Entity.Role.Code')">
              <el-input
                v-model="customerModel.code"
                :placeholder="tt('Entity.Role.Code')"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" :label="tt('Entity.Role.Name')">
              <el-input v-model="customerModel.name" :placeholder="tt('Entity.Role.Code')" />
            </el-form-item>
          </el-col>

          <!-- <el-col :span="formColSpan">
            <el-form-item prop="enumFlags" :label="tt('Entity.Role.EnumFlags')">
              <el-select
                v-model="employeeModel.enumFlags"
                multiple
                clearable
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="3"
                :placeholder="tt('Entity.Employee.EnumStatus')"
              >
                <el-option
                  v-for="item in employeeFlags"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col> -->

          <el-col :span="24">
            <el-form-item prop="remark" :label="tt('Entity.Role.Remark')">
              <el-input
                v-model="customerModel.remark"
                type="textarea"
                :placeholder="tt('Entity.Role.Remark')"
                rows="3"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
  </div>
</template>
