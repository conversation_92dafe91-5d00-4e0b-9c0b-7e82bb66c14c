<script setup lang="ts">
import { logApi } from "@/api/log";
import { selectorApi } from "@/api/selector";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import View from "./dialogs/view.vue";
import { isNull } from "@pureadmin/utils";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;
defineOptions({
  name: "log:Query"
});

/**
 * 基本配置定义
 */
const userStore = useUserStoreHook();
const cfg = reactive({
  enums: {
    gender: [],
    employeeStatus: []
  },
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    height: computed(() => {
      const height = listContainerRef.value?.clientHeight;
      return height > 700 ? height - 200 : 400;
    }),
    defaultSort: { prop: "createTime", order: "descending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.query ? 45 : 0))
    }
  },
  btn: {
    query: computed(() => {
      return userStore.hasAnyAuth(["Log", "Log:Query"]);
    })
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  },
  getlogsSelector: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  message: "",
  category: "",
  operate: "",
  userDisplayName: "",
  logTime: null,
  level: ""
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 当前组件ref
 */
const filterRef = ref();
const listContainerRef = ref();
const listRef = ref();
const viewRef = ref();
/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});
/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initFilter();
  query();
};
// 当日期范围为空时，将数组清空
const handleDateChange = (value: any) => {
  if (!value || value.length === 0) {
    filter.logTime = [];
    filter.logTime.length = 0;
  }
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);
  const initLogsSelector = async () => {
    return new Promise<void>(resolve => {
      logApi.GetlogsSelector().then(res => {
        cfg.getlogsSelector = res.datas;
        resolve();
      });
    });
  };
  const allInits = [initLogsSelector()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  logApi
    .QueryLog(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};
/**
 * 序号
 */
const indexMethod = index => {
  return (filter.pageIndex - 1) * filter.pageSize + index + 1;
};

/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.message"
              clearable
              :placeholder="tt('Entity.Log.Message')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.category"
              clearable
              :placeholder="tt('Entity.Log.Category')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-select
              v-model="filter.operate"
              clearable
              :placeholder="tt('Entity.Log.LogOperate')"
            >
              <el-option
                v-for="item in cfg.getlogsSelector"
                :key="item.id"
                :label="item.operate"
                :value="item.operate"
              />
            </el-select>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.userDisplayName"
              clearable
              :placeholder="tt('Entity.Log.UserDisplayName')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="8">
            <el-date-picker
              v-model="filter.logTime"
              type="datetimerange"
              :start-placeholder="t('filter.startDate')"
              :end-placeholder="t('filter.endDate')"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              date-format="YYYY-MM-DD"
              time-format="HH:mm:ss"
              @change="handleDateChange"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.level"
              clearable
              :placeholder="tt('Entity.Log.Level')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon('ep:search')"
              @click="query"
            >
              {{ t("operate.query") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          :index="indexMethod"
          type="index"
          align="center"
          width="70"
        />
        <el-table-column
          fixed
          sortable="custom"
          prop="message"
          :label="tt('Entity.Log.Message')"
          min-width="200"
        />
        <el-table-column
          fixed
          sortable="custom"
          prop="category"
          :label="tt('Entity.Log.Category')"
          min-width="100"
        />
        <el-table-column
          sortable="custom"
          prop="level"
          :label="tt('Entity.Log.Level')"
          width="90"
        />
        <el-table-column
          sortable="custom"
          prop="operate"
          :label="tt('Entity.Log.LogOperate')"
          width="120"
        >
          <template v-slot="{ row }">
            {{ row.operate }}
          </template>
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="logTime"
          :label="tt('Entity.Log.LogTime')"
          width="180"
          align="center"
        />
        <el-table-column
          sortable="custom"
          prop="userDisplayName"
          :label="tt('Entity.Log.UserDisplayName')"
          width="120"
        />
        <el-table-column
          fixed="right"
          :label="t('list.operate')"
          class-name="operate"
          :width="cfg.list.operate.width"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              class="view"
              size="small"
              :circle="true"
              :title="t('operate.view')"
              :icon="useRenderIcon('ep:document')"
              @click="viewRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <View ref="viewRef" @refresh="getList" />
  </div>
</template>
