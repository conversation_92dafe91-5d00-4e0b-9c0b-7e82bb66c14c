/**
 * 框架改造
 * 删除 @/utils/http
 * 创建 httpApi.ts
 */
import Axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  type CustomParamsSerializer
} from "axios";
import { stringify } from "qs";
import NProgress from "@/utils/progress";
import { getAuthToken, setAuthToken, formatAuthToken, removeAuthToken } from "@/utils/auth";
import {
  type Action,
  type ElMessageBoxOptions,
  ElMessage,
  ElMessageBox,
  type messageType,
  type MessageParams
} from "element-plus";
import { $t, transformI18n } from "@/plugins/i18n";
import { decodeCycleJson } from "@/utils/json";
import { ref } from "vue";
import qs from "qs";
import { goToRoute } from "@/router/utils";

// import { useUserStoreHook } from "@/store/modules/user";

const { VITE_API_BASE_URL } = import.meta.env;

// 相关配置请参考：www.axios-js.com/zh-cn/docs/#axios-request-config-1
const defaultConfig: AxiosRequestConfig = {
  // 请求超时时间
  timeout: 10000,
  baseURL: VITE_API_BASE_URL, //API基础地址
  headers: {
    Accept: "application/json, text/plain, */*",
    "Content-Type": "application/json",
    "X-Requested-With": "XMLHttpRequest"
  },
  // 数组格式参数序列化（https://github.com/axios/axios/issues/5142）
  paramsSerializer: {
    serialize: stringify as unknown as CustomParamsSerializer
  }
};

const initConfig: ApiRequestConfig = {
  messageResult: true
};

const apiServices: Record<string, any> = {};

export class HttpApi {
  private controller: string;

  /** 当前`Axios`实例对象 */
  private axiosInstance: AxiosInstance;

  constructor(controller: string, baseUrl?: string) {
    const config = ref({ ...defaultConfig });

    if (!baseUrl) {
      baseUrl = config.value.baseURL;
    } else {
      config.value.baseURL = baseUrl;
    }

    this.controller = controller;

    if (apiServices.hasOwnProperty(baseUrl)) {
      this.axiosInstance = apiServices[baseUrl];
    } else {
      this.axiosInstance = Axios.create(config.value);
      apiServices[baseUrl] = this.axiosInstance;
      this.httpInterceptorsRequest(this.axiosInstance);
      this.httpInterceptorsResponse(this.axiosInstance);
    }
  }

  /** 初始化配置对象 */

  protected beforeRequestFunc(_config: ApiRequestConfig): void {
    // console.log(_config);
  }

  protected beforeResponseFunc(response: ApiResponse): void {
    if (response.headers?.jwt) {
      setAuthToken(response.headers.jwt);
    }

    const result = decodeCycleJson(response.data);

    if (result.hasOwnProperty("data") && result.data?.hasOwnProperty("datas")) {
      Object.keys(result.data).forEach(prop => {
        result[prop] = result.data[prop];
      });

      delete result.data;
    }

    const messageResult = response.config?.messageResult ?? initConfig.messageResult;
    const messageBoxResult = response.config?.messageBoxResult ?? initConfig.messageBoxResult;

    if (messageResult || messageBoxResult) {
      // 需要显示消息

      if (
        result.hasOwnProperty("success") &&
        result.hasOwnProperty("type") &&
        typeof result.type === "number" &&
        result.hasOwnProperty("messages") &&
        result.messages instanceof Array &&
        result.messages.length > 0 &&
        result.type !== -1
      ) {
        const message = result.messages.toString().replace(/,/g, "<br/>");

        if (result.success && !messageBoxResult) {
          // 仅提示
          ElMessage({
            message,
            type: "success",
            duration: 3 * 1000
          });
        } else {
          let type: messageType = "success";
          let title = transformI18n($t("titles.info"));

          switch (result.type) {
            case -2:
              type = "warning";
              title = transformI18n($t("titles.warning"));
              break;
            case -3:
              type = "error";
              title = transformI18n($t("titles.error"));
              break;
          }
          // 提示+确认按钮,confirm不处理
          const option: ElMessageBoxOptions = {
            dangerouslyUseHTMLString: true,
            confirmButtonText: transformI18n($t("buttons.pureClose")),
            showConfirmButton: true,
            showCancelButton: false,
            showClose: false,
            message,
            type: type,
            title: title,
            callback: (_action: Action) => {}
          };

          ElMessageBox(option);
        }
      }
    }
  }

  protected beforeErrorFunc(error: ApiError): boolean {
    if (error.message.includes("timeout")) {
      error.message = "请求超时，请联系管理员检查。";
    } else if (error.code == "ECONNABORTED") {
      error.message = "网络连接错误，请检查您的网络。";
    }

    let abort = false;

    const options: MessageParams = {
      type: "error",
      duration: 5 * 1000
    };

    if (error?.response) {
      switch (error.response.status) {
        case 400:
          error.message = "请求错误";
          break;
        case 401:
          error.message = "未授权，请登录";
          abort = true;
          options.onClose = () => {
            removeAuthToken();
            goToRoute("/login");
          };
          break;
        case 403:
          error.message = "拒绝访问";
          break;
        case 404:
          error.message = `请求地址出错: ${error.response.config.url}`;
          break;
        case 408:
          error.message = "请求超时";
          break;
        case 500:
          error.message = `系统处理过程遇到错误，请联系管理员检查。错误信息为:  ${error.response.data}`;
          break;
        case 501:
          error.message = "服务未实现";
          break;
        case 502:
          error.message = "网关错误";
          break;
        case 503:
          error.message = "服务不可用";
          break;
        case 504:
          error.message = "网关超时";
          break;
        case 505:
          error.message = "HTTP版本不受支持";
          break;
        default:
          break;
      }
    }

    if (error.message) {
      options.message = error.message;
      ElMessage(options);
    }

    // todo: 刷新Token等逻辑可能在此实现
    console.log(error);

    return !abort;
  }

  /** `token`过期后，暂存待执行的请求 */
  private requests = [];

  /** 防止重复刷新`token` */
  private static isRefreshing = false;

  /** 保存当前`Axios`实例对象 */
  // private static axiosInstance: AxiosInstance = Axios.create(defaultConfig);

  /** 重连原始请求 */
  private retryOriginalRequest(config: ApiRequestConfig) {
    return new Promise(resolve => {
      this.requests.push((token: string) => {
        config.headers["Authorization"] = formatAuthToken(token);
        resolve(config);
      });
    });
  }

  /** 请求拦截 */
  private httpInterceptorsRequest(axiosInstance: AxiosInstance): void {
    axiosInstance.interceptors.request.use(
      async (config: ApiRequestConfig): Promise<any> => {
        // 开启进度条动画
        // if (!NProgress.isStarted()) {
        //   NProgress.start();
        // }
        NProgress.start();

        this.beforeRequestFunc(config);

        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof config.beforeRequestCallback === "function") {
          config.beforeRequestCallback(config);
        } else if (initConfig.beforeRequestCallback) {
          initConfig.beforeRequestCallback(config);
        }
        /** 请求白名单，放置一些不需要`token`的接口（通过设置请求白名单，防止`token`过期后再请求造成的死循环问题） */
        const whiteList = ["/refresh-token", "/login"];
        return whiteList.some(url => config.url.endsWith(url))
          ? config
          : new Promise(resolve => {
              const authToken = getAuthToken();
              if (authToken) {
                // const now = new Date().getTime();
                // const expired = parseInt(data.expires) - now <= 0;
                // if (expired) {
                //   if (!HttpApi.isRefreshing) {
                //     HttpApi.isRefreshing = true;
                //     // token过期刷新
                //     useUserStoreHook()
                //       .handRefreshToken({ refreshToken: data.refreshToken })
                //       .then(res => {
                //         const token = res.data.accessToken;
                //         config.headers["Authorization"] = formatToken(token);
                //         HttpApi.requests.forEach(cb => cb(token));
                //         HttpApi.requests = [];
                //       })
                //       .finally(() => {
                //         HttpApi.isRefreshing = false;
                //       });
                //   }
                //   resolve(HttpApi.retryOriginalRequest(config));
                // } else {
                config.headers["Authorization"] = formatAuthToken(authToken);
                resolve(config);
                // }
              } else {
                resolve(config);
              }
            });
      },
      error => {
        return Promise.reject(error);
      }
    );
  }

  /** 响应拦截 */
  private httpInterceptorsResponse(axiosInstance: AxiosInstance): void {
    axiosInstance.interceptors.response.use(
      (response: ApiResponse) => {
        const $config = response.config;
        // 关闭进度条动画
        NProgress.done();
        // 优先判断post/get等方法是否传入回调，否则执行初始化设置等回调
        if (typeof $config?.beforeResponseCallback === "function") {
          $config.beforeResponseCallback(response);
        } else if (initConfig.beforeResponseCallback) {
          initConfig.beforeResponseCallback(response);
        }
        this.beforeResponseFunc(response);
        return response.data;
      },
      (error: ApiError) => {
        const $error = error;
        $error.isCancelRequest = Axios.isCancel($error);
        // 关闭进度条动画
        NProgress.done();

        if (this.beforeErrorFunc($error)) {
          // 所有的响应异常 区分来源为取消请求/非取消请求
          return Promise.reject($error);
        } else {
          return;
        }
      }
    );
  }

  /** 通用请求工具函数 */
  public request<T>(
    method: RequestMethods,
    url: string,
    param?: AxiosRequestConfig,
    axiosConfig?: ApiRequestConfig
  ): Promise<T> {
    const config = {
      method,
      url,
      ...param,
      ...axiosConfig
    } as ApiRequestConfig;

    if (config.method === "get") {
      config.paramsSerializer = params => {
        return qs.stringify(params, { arrayFormat: "repeat" });
      };
    }

    // 单独处理自定义请求/响应回调
    return new Promise((resolve, reject) => {
      this.axiosInstance
        .request(config)
        .then((response: undefined) => {
          resolve(response);
        })
        .catch(error => {
          reject(error);
        });
    });
  }

  /** 单独抽离的`post`工具函数 */
  public post<T>(action: string, data?: object, config?: ApiRequestConfig): Promise<T> {
    const url = this.controller + "/" + action;
    return this.request<T>("post", url, { data }, config);
  }

  /** 单独抽离的`get`工具函数 */
  public get<T>(action: string, params?: object, config?: ApiRequestConfig): Promise<T> {
    const url = this.controller + "/" + action;
    //    const { params, headers, processResult } = option
    return this.request<T>("get", url, { params }, config);
  }
}

export function useHttpApi(controller: string, baseUrl?: string): HttpApi {
  const httpApi = new HttpApi(controller, baseUrl);
  return httpApi;
}
