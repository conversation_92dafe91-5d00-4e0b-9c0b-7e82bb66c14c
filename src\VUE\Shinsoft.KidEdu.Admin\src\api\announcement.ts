import { HttpApi } from "./libs/httpApi";

const controller = "Management";

const api = new HttpApi(controller);

export const announcementApi = {
  QueryAnnouncement(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryAnnouncement", data, config);
  },
  GetAnnouncement(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetAnnouncement", { id }, config);
  },
  AddAnnouncement(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddAnnouncement", data, config);
  },
  UpdateAnnouncement(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateAnnouncement", data, config);
  },
  DelectAnnouncement(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("DelectAnnouncement", { id: data.id }, config);
  }
};
