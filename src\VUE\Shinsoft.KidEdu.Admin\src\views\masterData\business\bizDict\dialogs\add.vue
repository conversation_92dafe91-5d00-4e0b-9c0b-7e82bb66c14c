<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { ElMessage, FormInstance } from "element-plus";
import { allowEdit } from "@/utils/auth";
import { bizMasterDataApi } from "@/api/bizMasterData";

defineOptions({
  name: "bizDict:add"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return "新建字典";
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumFlags: 0,
      enumEditFlags: -1
    }
  },
  // 枚举定义
  enums: {
    roleFlag: []
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? allowEdit(state.model, 2 | 4 | 16 | 64 | 128) &&
            userStore.hasAnyAuth(["Role:Manage", "Role:Manage:Edit"])
        : userStore.hasAnyAuth(["Role:Manage", "Role:Manage:Add"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>
});

/**
 * 验证规则
 */
const rules = {
  form: {
    code: [{ max: 50, message: "编码长度不可以超过50个字符", trigger: "blur" }],
    name: [
      { required: true, message: "请输入名称", trigger: "blur" },
      { max: 50, message: "姓名长度不可以超过50个字符", trigger: "blur" }
    ],
    shortname: [
      { required: true, message: "请输入名称", trigger: "blur" },
      { max: 50, message: "姓名长度不可以超过50个字符", trigger: "blur" }
    ],
    ordinal: [{ max: 50, message: "排序长度不可以超过50个字符", trigger: "blur" }],
    remark: [{ max: 500, message: "备注长度不可以超过500个字符", trigger: "blur" }]
  }
};

/**
 * 存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件
 */
const init = () => {
  initState();
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 新增数据
 */
const add = () => {
  cfg.loading.btn = true;

  const data = getModel();

  bizMasterDataApi
    .AddBizDict(data)
    .then(res => {
      // 仅提示
      console.log(data);
      if (res.success) {
        refresh("新增成功", "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      add();
    }
  });
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.model.parentId = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="code" label="编码">
              <el-input
                v-model="state.model.code"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 2)"
                placeholder="编码"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" label="名称">
              <el-input
                v-model="state.model.name"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 4)"
                placeholder="名称"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="shortName" label="简称">
              <el-input
                v-model="state.model.shortName"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 4)"
                placeholder="名称"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="ordinal" label="排序">
              <el-input
                v-model="state.model.ordinal"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 128)"
                placeholder="备注"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="remark" label="备注">
              <el-input
                v-model="state.model.remark"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 64)"
                placeholder="备注"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            保存
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="css">
.is-not-error > .el-input__wrapper {
  box-shadow: #dcdfe6 !important;
}
</style>
