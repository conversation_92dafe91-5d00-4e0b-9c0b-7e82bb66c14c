import { useHttpApi } from "./libs/httpApi";
// import { useHttpApi } from "@/utils/httpApi";

const controller = "sys";

const api = useHttpApi(controller);

export const sysApi = {
  NewGuid(config?: ApiRequestConfig) {
    return api.get<string>("NewGuid", null, config);
  },
  NewGuids(count?: number, config?: ApiRequestConfig) {
    return api.get<string>("NewGuids", { count }, config);
  },
  UserLogin(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UserLogin", data, config);
  },
  GetCurrentUser(config?: ApiRequestConfig) {
    return api.get<BizResult>("GetCurrentUser", null, config);
  },
  SwitchMyIdentity(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("SwitchMyIdentity", data, config);
  },
  SwitchToAgent(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("SwitchToAgent", data, config);
  },
  SwitchCulture(culture: string, config?: ApiRequestConfig) {
    const data = { culture };
    return api.post<BizResult>("SwitchCulture", data, config);
  }
};
