import { $t } from "@/plugins/i18n";
const { VITE_HIDE_HOME } = import.meta.env;
const Layout = () => import("@/layout/index.vue");
// import { useI18n } from "vue-i18n";
// const { t } = useI18n();
// const tt = t;

export default {
  path: "/",
  name: "Home",
  component: Layout,
  redirect: "/welcome",
  meta: {
    icon: "ep:home-filled",
    title: $t("menus.pureHome"),
    rank: 0
  },
  children: [
    {
      path: "/welcome",
      name: "Welcome",
      component: () => import("@/views/welcome/index.vue"),
      meta: {
        title: $t("menus.pureHome"),
        showLink: VITE_HIDE_HOME === "true" ? false : true
      }
    },
    {
      path: "/announcement/more",
      name: "moreAnnouncement",
      component: () => import("@/views/welcome/announcement/view.vue"),
      meta: {
        title: "",
        showLink: false
      }
    }
  ]
} satisfies RouteConfigsTable;
