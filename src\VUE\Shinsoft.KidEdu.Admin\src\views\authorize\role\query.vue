<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import { EnumFlagsSelect } from "@/components/EnumFlags";
import { allowEdit } from "@/utils/auth";
import ViewDialog from "./dialogs/view.vue";
import EditDialog from "./dialogs/edit.vue";
import AuthDialog from "./dialogs/auth.vue";
import MemberDialog from "./dialogs/member.vue";
import { useI18n } from "vue-i18n";
import { useResizeObserver } from "@vueuse/core";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:query"
});

/**
 * 当前组件ref
 */
const filterRef = ref();
const listContainerRef = ref();
const listRef = ref();
const viewRef = ref();
const editRef = ref();
const authRef = ref();
const memberRef = ref();

/**
 * 基本配置定义
 */
const cfg = reactive({
  enums: {
    roleFlag: []
  },
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    height: 0,
    // computed(() => {
    //   const height = listContainerRef.value?.clientHeight;
    //   return height > 600 ? height - 200 : 400;
    // }),
    defaultSort: { prop: "name", order: "ascending" },
    operate: {
      width: computed(
        () =>
          40 +
          (cfg.btn.edit ? 45 : 0) +
          (cfg.btn.delete ? 45 : 0) +
          (cfg.btn.auth ? 45 : 0) +
          (cfg.btn.member ? 45 : 0)
      )
    }
  },
  btn: {
    add: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Add"]);
    }),
    edit: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Edit"]);
    }),
    delete: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Delete"]);
    }),
    auth: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:auth"]);
    }),
    member: computed(() => {
      return userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:member"]);
    })
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});
onMounted(() => {
  useResizeObserver(listContainerRef.value, entries => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    cfg.list.height = height - 190;
  });
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: "",
  flags: 0
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;
  const initRoleFlag = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("RoleFlag", "Query").then(enumInfos => {
        cfg.enums.roleFlag = enumInfos;
        resolve();
      });
    });
  };

  const allInits = [initRoleFlag()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  authorizeApi
    .QueryRole(filter)
    .then(res => {
      setModel(res);
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 设置model数据
 */
const setModel = (res: QueryResult) => {
  state.datas = res.datas;
  state.total = res.total;
  filter.pageIndex = res.pageIndex;
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filterRef.value.validate((valid, fields) => {
    if (valid) {
      filter.pageIndex = 1;
      getList();
    }
  });
};

/**
 * 新增：点击【新增】按钮事件
 */
const addRow = () => {
  editRef.value?.open();
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editRef.value?.open(row.id);
};

/**
 * 授权：点击列表中【授权】按钮事件
 */
const authRow = (row: any) => {
  authRef.value?.open(row.id);
};

/**
 * 维护成员：点击列表中【维护成员】按钮事件
 */
const memberRow = (row: any) => {
  memberRef.value?.open(row.id, row.name);
};

/**
 * 删除：点击列表中【删除】按钮事件
 */
const deleteRow = (row: any) => {
  viewRef.value?.del(row);
};

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-input
                v-model="filter.keywords"
                clearable
                :placeholder="t('filter.keywords')"
                class="filter-item"
                @keyup.enter="query"
              />
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <enum-flags-Select
                v-model="filter.flags"
                enumType="RoleFlag"
                :enums="cfg.enums.roleFlag"
                collapse-tags
                collapse-tags-tooltip
                :max-collapse-tags="1"
                :placeholder="tt('Entity.Role.EnumFlags')"
              />
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon('ep:search')"
              @click="query"
              >{{ t("operate.query") }}</el-button
            >
          </el-col>
        </el-row>
        <el-row :gutter="cfg.filter.gutter" type="flex" justify="end">
          <el-col :span="24" class="buttonbar">
            <el-button
              v-if="cfg.btn.member"
              class="new"
              :icon="useRenderIcon('ep:document-add')"
              @click="addRow"
            >
              {{ t("operate.add") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          show-overflow-tooltip
          width="60"
          align="center"
        >
          <template v-slot="scope">
            <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed
          sortable="custom"
          prop="code"
          :label="tt('Entity.Role.Code')"
          width="120"
        />
        <el-table-column
          fixed
          sortable="custom"
          prop="name"
          :label="tt('Entity.Role.Name')"
          width="200"
        />
        <el-table-column
          sortable="custom"
          sort-by="enumFlags"
          prop="enumFlagsDesc"
          :label="tt('Entity.Role.EnumFlags')"
          width="150"
        />
        <el-table-column
          sortable="custom"
          prop="remark"
          :label="tt('Entity.Role.Remark')"
          min-width="200"
        />
        <el-table-column
          fixed="right"
          :label="t('list.operate')"
          class-name="operate"
          :width="cfg.list.operate.width"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              class="view"
              size="small"
              :circle="true"
              :title="t('operate.view')"
              :icon="useRenderIcon('ep:document')"
              @click="viewRow(row)"
            />
            <el-button
              v-if="cfg.btn.edit && allowEdit(row, 2 | 4 | 16 | 64)"
              class="edit"
              size="small"
              :circle="true"
              :title="t('operate.edit')"
              :icon="useRenderIcon('ep:edit')"
              @click="editRow(row)"
            />
            <el-button
              v-if="cfg.btn.auth"
              class="edit"
              size="small"
              :circle="true"
              :title="t('role.operate.auth')"
              :icon="useRenderIcon('ep:lock')"
              @click="authRow(row)"
            />
            <el-button
              v-if="cfg.btn.member"
              class="edit"
              size="small"
              :circle="true"
              :title="t('role.operate.member')"
              :icon="useRenderIcon('ep:user')"
              @click="memberRow(row)"
            />
            <el-button
              v-if="cfg.btn.delete && allowEdit(row, 1)"
              class="delete"
              size="small"
              :circle="true"
              :title="t('operate.delete')"
              :icon="useRenderIcon('ep:delete')"
              @click="deleteRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <view-dialog ref="viewRef" @refresh="getList" />
    <edit-dialog ref="editRef" @refresh="getList" />
    <auth-dialog ref="authRef" />
    <member-dialog ref="memberRef" />
  </div>
</template>
