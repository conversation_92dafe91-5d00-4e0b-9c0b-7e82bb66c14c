<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { bizMasterDataApi } from "@/api/bizMasterData";
import { getDefaultOrder } from "@/utils/table";

defineOptions({
  name: "bizDict:veiw"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "40%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 24
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return "查看字典";
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      name: "",
      shortName: "",
      code: "",
      ordinal: "",
      remark: ""
    }
  },
  // 枚举定义
  enums: {
    roleFlag: []
  },
  list: {
    height: 300,
    defaultSort: { prop: "name", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0))
    }
  }, // loading：控制变量
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  datas: [],
  model: cfg.default.model as Record<string, any>
});

/**
 * 存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件
 */
const init = () => {
  initFilter();
  query();
  get();
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    bizMasterDataApi
      .GetBizDict(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  bizMasterDataApi
    .QueryBizDict(filter)
    .then(res => {
      state.datas = res.datas;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  //cfg.loading.filter = true;
};

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  id: ""
});

/**
 * 初始化state数据
 */
const initState = () => {
  state.datas = cfg.default.model;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 按钮事件：【删除】
 */
const del = (data?: any) => {
  // 内部调用时，data为空
  // 外部调用时，需要传入{id:""}的对象
  data = data ?? getModel();

  if (data && data.id) {
    ElMessageBox.confirm(
      `请确认是否要删除【${data.name}】?<br />提示：删除后将不可恢复!`,
      "删除确认",
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: "确认",
        cancelButtonText: "取消",
        type: "error"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        bizMasterDataApi
          .DelectBizDict(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh("删除成功", "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open,
  del
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      v-model:title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <el-form
          ref="formRef"
          v-loading="cfg.loading.form"
          :model="state.model"
          label-position="right"
          :label-width="formLabelWidth"
          class="el-dialog-form"
        >
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="name" label="名称">
                <span>{{ state.model.name }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="shortName" label="简称">
                <span>{{ state.model.shortName }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="code" label="编码">
                <span>{{ state.model.code }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="ordinal" label="排序">
                <span>{{ state.model.ordinal }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="remark" label="备注">
                <span>{{ state.model.remark }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>

      <template #footer>
        <div>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
