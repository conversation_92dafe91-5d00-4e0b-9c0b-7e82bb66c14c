<script setup lang="ts">
import { useNav } from "@/layout/hooks/useNav";
import LaySearch from "../lay-search/index.vue";
import LayNotice from "../lay-notice/index.vue";
import LayNavMix from "../lay-sidebar/NavMix.vue";
import { useTranslationLang } from "@/layout/hooks/useTranslationLang";
import LaySidebarFullScreen from "../lay-sidebar/components/SidebarFullScreen.vue";
import LaySidebarBreadCrumb from "../lay-sidebar/components/SidebarBreadCrumb.vue";
import LaySidebarTopCollapse from "../lay-sidebar/components/SidebarTopCollapse.vue";

import GlobalizationIcon from "@/assets/svg/globalization.svg?component";
import AccountSettingsIcon from "@iconify-icons/ri/user-settings-line";
import LogoutCircleRLine from "@iconify-icons/ri/logout-circle-r-line";
import Setting from "@iconify-icons/ri/settings-3-line";
import Check from "@iconify-icons/ep/check";
import { getConfig } from "@/config";
import { useUserStoreHook } from "@/store/modules/user";
import { userInfo } from "@/store/types/user";
import { initRouter } from "@/router/utils";
import { useMultiTagsStoreHook } from "@/store/modules/multiTags";
import EditPwdDialog from "@/views/account/dialogs/editPwd.vue";
import MyDelegateDialog from "@/views/account/dialogs/myDelegate.vue";
import { selectorApi } from "@/api/selector";
import Chinese from "@iconify-icons/icon-park-outline/chinese";
import English from "@iconify-icons/icon-park-outline/english";

const {
  layout,
  device,
  logout,
  onPanel,
  pureApp,
  username,
  userAvatar,
  avatarsStyle,
  toggleSideBar,
  getDropdownItemStyle,
  getDropdownItemClass
} = useNav();

const userStore = useUserStoreHook();

const { t, locale, translationCulture } = useTranslationLang();

const Title = getConfig().Title;

const cfg = reactive({
  search: false,
  fullScreen: false,
  notice: false,
  setting: false,
  loading: {
    select: false
  }
});

const state = reactive({
  currentCompanyId: userStore.currentCompanyId,
  currentEmployeeId: userStore.currentEmployeeId,
  cultures: []
});

const router = useRouter();

const refreshToHome = () => {
  state.currentCompanyId = userStore.currentCompanyId;
  state.currentEmployeeId = userStore.currentEmployeeId;
  initRouter().then(() => {
    useMultiTagsStoreHook().clearAllTags();
    router.push("/");
  });
};

const companyChange = value => {
  if (userStore.myCompanies[value].identities?.length > 0) {
    cfg.loading.select = true;
    userStore
      .switchMyIdentity(userStore.myCompanies[value].identities[0].id)
      .then(() => {
        refreshToHome();
      })
      .finally(() => {
        cfg.loading.select = false;
      });
  }
};

const identityChange = value => {
  let switchMethod: (newIdentityId: any) => Promise<userInfo> = null;

  if (userStore.identities.some(p => p.id === value)) {
    switchMethod = userStore.switchMyIdentity;
  } else if (userStore.delegates.some(p => p.id === value)) {
    switchMethod = userStore.switchToAgent;
  }

  if (switchMethod) {
    cfg.loading.select = true;
    switchMethod(value)
      .then(() => {
        refreshToHome();
      })
      .finally(() => {
        cfg.loading.select = false;
      });
  }
};

const editPwdRef = ref();
const myDelegateRef = ref();

const editPwd = () => {
  editPwdRef.value.open();
};

const setMyDelegate = () => {
  myDelegateRef.value.open();
};

const init = () => {
  selectorApi.GetCultures().then(res => {
    state.cultures = res.data;
  });
};
init();
</script>

<template>
  <div class="navbar bg-[#163c6d] shadow-sm shadow-[rgba(0,21,41,0.08)]">
    <LaySidebarTopCollapse
      v-if="device === 'mobile'"
      class="hamburger-container"
      :is-active="pureApp.sidebar.opened"
      @toggleClick="toggleSideBar"
    />

    <!-- <LaySidebarBreadCrumb
      v-if="layout !== 'mix' && device !== 'mobile'"
      class="breadcrumb-container"
    /> -->

    <div class="title">{{ Title }}</div>

    <LayNavMix v-if="layout === 'mix'" />

    <div v-if="layout === 'vertical'" class="vertical-header-right">
      <!-- 菜单搜索 -->
      <LaySearch v-if="cfg.search" id="header-search" />
      <!-- 国际化 -->
      <el-dropdown v-if="state.cultures.length > 1" id="header-translation" trigger="click">
        <span>
          <IconifyIconOffline
            v-show="locale === 'zh-CN'"
            :icon="Chinese"
            class="navbar-bg-hover w-[48px] h-[48px] p-[12px] cursor-pointer outline-none text-white"
          />
          <IconifyIconOffline
            v-show="locale === 'en'"
            :icon="English"
            class="navbar-bg-hover w-[48px] h-[48px] p-[12px] cursor-pointer outline-none text-white"
          />
          <GlobalizationIcon
            v-show="locale !== 'zh-CN' && locale !== 'en'"
            class="navbar-bg-hover w-[40px] h-[48px] p-[11px] cursor-pointer outline-none text-white"
          />
        </span>
        <template #dropdown>
          <el-dropdown-menu class="translation">
            <el-dropdown-item
              v-for="item in state.cultures"
              :key="item.culture"
              :style="getDropdownItemStyle(locale, item.culture)"
              :class="['dark:!text-white', getDropdownItemClass(locale, item.culture)]"
              @click="translationCulture(item.culture)"
            >
              <IconifyIconOffline v-show="locale === item.culture" class="check" :icon="Check" />
              {{ item.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 全屏 -->
      <LaySidebarFullScreen v-if="cfg.fullScreen" id="full-screen" />
      <!-- 消息通知 -->
      <LayNotice v-if="cfg.notice" id="header-notice" />
      <el-space spacer="-" :size="0">
        <!-- 公司信息 -->
        <el-select
          v-if="userStore.myCompanies && Object.keys(userStore.myCompanies).length > 1"
          v-model="state.currentCompanyId"
          :loading="cfg.loading.select"
          class="company-select"
          :placeholder="t('shinsoft.select')"
          @change="companyChange"
        >
          <el-option
            v-for="companyId in Object.keys(userStore.myCompanies)"
            :key="companyId"
            :label="userStore.myCompanies[companyId].name"
            :value="companyId"
          />
        </el-select>
        <span v-else class="user-info-text">
          <p v-if="userStore.currentCompanyName" class="dark:text-white">
            {{ userStore.currentCompanyName }}
          </p>
        </span>
        <!-- 用户信息 -->
        <el-select
          v-if="userStore.identities.length > 1 || userStore.delegates.length > 0"
          v-model="state.currentEmployeeId"
          :loading="cfg.loading.select"
          class="identity-select"
          placeholder="请选择"
          @change="identityChange"
        >
          <template #label="{ label, value }">
            <span>{{ label }} </span>
            <span
              v-if="userStore.delegates.some(p => p.id === value && p.isCurrent)"
              style="float: right; font-weight: bold"
              >【t("shinsoft.delegating")】</span
            >
          </template>
          <el-option
            v-for="item in userStore.identities"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
            <span style="float: left">{{ item.name }}</span>
            <span
              v-if="item.isCurrent"
              style="float: right; color: var(--el-text-color-secondary); font-size: 13px"
            >
              {{ t("shinsoft.current") }}
            </span>
          </el-option>
          <el-option-group v-if="userStore.delegates.length > 0" :label="t('shinsoft.delegate')">
            <el-option
              v-for="item in userStore.delegates"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
              <span style="float: left">{{ item.name }}</span>
              <span
                v-if="item.isCurrent"
                style="float: right; color: var(--el-text-color-secondary)"
              >
                {{ t("shinsoft.delegating") }}
              </span>
              <span v-else style="float: right; color: var(--el-text-color-secondary)">
                {{ t("shinsoft.delegateAble") }}
              </span>
            </el-option>
          </el-option-group>
        </el-select>
        <span v-else class="user-info-text">
          <img v-if="userAvatar" :src="userAvatar" :style="avatarsStyle" />
          <p v-if="username" class="dark:text-white">{{ username }}</p>
        </span>
      </el-space>
      <el-dropdown v-if="userStore.isAgent === false" trigger="click">
        <span
          class="el-dropdown-link set-icon navbar-bg-hover select-none"
          :title="t('shinsoft.accountSettings')"
        >
          <icon-material-symbols-manage-accounts />
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="editPwd">
              <icon-carbon-password />
              {{ t("shinsoft.editPwd") }}
            </el-dropdown-item>
            <el-dropdown-item @click="setMyDelegate">
              <icon-mdi-account-switch-outline />
              {{ t("shinsoft.setDelegate") }}
            </el-dropdown-item>
            <el-dropdown-item v-if="false" @click="logout">
              <IconifyIconOffline :icon="LogoutCircleRLine" />
              {{ t("buttons.pureLoginOut") }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <!-- 退出登录 -->
      <span class="set-icon navbar-bg-hover" :title="t('buttons.pureLoginOut')" @click="logout">
        <IconifyIconOffline :icon="LogoutCircleRLine" />
      </span>
      <span
        v-if="cfg.setting"
        class="set-icon navbar-bg-hover"
        :title="t('buttons.pureOpenSystemSet')"
        @click="onPanel"
      >
        <IconifyIconOffline :icon="Setting" />
      </span>
    </div>
  </div>
  <edit-pwd-dialog ref="editPwdRef" />
  <my-delegate-dialog ref="myDelegateRef" />
</template>

<style lang="scss" scoped>
.company-select {
  min-width: 250px;
  max-width: 300px;
  padding: 0px 10px;
}

.identity-select {
  min-width: 200px;
  max-width: 250px;
  padding: 0px 10px;
}

.title {
  float: left;
  font-size: 22px;
  color: #fff;
  line-height: 48px;
}

.user-info-text {
  display: flex;
  align-items: center;
  justify-content: space-around;
  height: 48px;
  padding: 10px;
  color: #fff;
}
.el-dropdown__popper {
  svg {
    margin-right: 10px;
  }
}

.navbar {
  width: 100%;
  height: 48px;
  overflow: hidden;

  .hamburger-container {
    float: left;
    height: 100%;
    line-height: 48px;
    cursor: pointer;
  }

  .vertical-header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    min-width: 280px;
    height: 48px;
    color: #fff;

    .el-dropdown-link {
      display: flex;
      align-items: center;
      justify-content: space-around;
      height: 48px;
      padding: 10px;
      color: #fff;
      cursor: pointer;

      p {
        font-size: 14px;
      }

      img {
        width: 22px;
        height: 22px;
        border-radius: 50%;
      }
    }
  }

  .breadcrumb-container {
    float: left;
    margin-left: 16px;
  }
}

.translation {
  ::v-deep(.el-dropdown-menu__item) {
    padding: 5px 40px;
  }

  .check {
    position: absolute;
    left: 20px;
  }

  .check-zh {
    position: absolute;
    left: 20px;
  }

  .check-en {
    position: absolute;
    left: 20px;
  }
}

.logout {
  width: 120px;

  ::v-deep(.el-dropdown-menu__item) {
    display: inline-flex;
    flex-wrap: wrap;
    min-width: 100%;
  }
}
</style>
