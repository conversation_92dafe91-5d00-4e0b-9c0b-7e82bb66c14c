/**
 * 框架改造
 * 新增用户信息类型
 */

export class userInfo extends Record<string, any> {
  // 用户唯一标识
  identityKey: identityKey;
  // 显示名称
  displayName: string;
  // 头像
  avatar?: string;
  // 称谓
  title?: string;
  // 邮箱
  email?: string;
  // 手机
  mobile?: string;
  //权限
  auths: Array<
    string | Record<string, boolean> | Record<string, string> | Record<string, Array<string>>
  >;
  // 终端
  program: number;
  // 用户ID
  userId: string;
  // 身份ID/被代理人ID（员工ID)
  employeeId?: string;
  // 代理人身份ID
  agentId?: string;
  // 角色ID
  roleId?: string;
}
