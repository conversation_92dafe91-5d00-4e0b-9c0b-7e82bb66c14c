<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import { selector<PERSON>pi } from "@/api/selector";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import View from "./dialogs/view.vue";
import Edit from "./dialogs/edit.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "position:query"
});

/**
 * 当前组件ref
 */
const filterRef = ref();
const listRef = ref();
const viewRef = ref();
const editRef = ref();
const listContainerRef = ref();

/**
 * 基本配置定义
 */
const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    height: computed(() => {
      const height = listContainerRef.value?.clientHeight;
      return height > 580 ? height - 180 : 400;
    }),
    defaultSort: { prop: "grade", order: "descending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0) + (cfg.btn.delete ? 45 : 0))
    }
  },
  btn: {
    add: computed(() => {
      return userStore.hasAnyAuth(["Position:Manage", "Position:Manage:Add"]);
    }),
    edit: computed(() => {
      return userStore.hasAnyAuth(["Position:Manage", "Position:Manage:Edit"]);
    }),
    delete: computed(() => {
      return userStore.hasAnyAuth(["Position:Manage", "Position:Manage:Delete"]);
    })
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  },
  positions: []
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: "",
  parentId: null
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;
  const initPositions = async () => {
    return new Promise<void>(resolve => {
      selectorApi.GetPositionSelectors().then(res => {
        cfg.positions = res.data;
        resolve();
      });
    });
  };

  const allInits = [initPositions()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  basicMasterDataApi
    .QueryPosition(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filterRef.value.validate((valid, fields) => {
    if (valid) {
      filter.pageIndex = 1;
      getList();
    }
  });
};

/**
 * 新增：点击【新增】按钮事件
 */
const addRow = () => {
  editRef.value?.open();
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editRef.value?.open(row.id);
};

/**
 * 删除：点击列表中【删除】按钮事件
 */
const deleteRow = (row: any) => {
  viewRef.value?.del(row);
};

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 序号
 */
const indexMethod = index => {
  return (filter.pageIndex - 1) * filter.pageSize + index + 1;
};
/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-input
              v-model="filter.keywords"
              clearable
              :placeholder="t('filter.keywords')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon('ep:search')"
              @click="query"
            >
              {{ t("operate.query") }}
            </el-button>
          </el-col>
        </el-row>
        <el-row type="flex" :gutter="cfg.filter.gutter" justify="end">
          <el-col :span="cfg.filter.span" class="buttonbar">
            <el-button
              v-if="cfg.btn.add"
              class="new"
              :icon="useRenderIcon('ep:document-add')"
              @click="addRow"
            >
              {{ t("operate.add") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          :index="indexMethod"
          type="index"
          align="center"
          width="70"
        />
        <el-table-column
          sortable="custom"
          prop="code"
          :label="tt('Entity.Position.Code')"
          width="260"
        />
        <el-table-column
          sortable="custom"
          prop="name"
          :label="tt('Entity.Position.Name')"
          min-width="200"
        />
        <el-table-column
          v-if="false"
          sortable="custom"
          prop="parent.Name"
          :label="tt('Entity.Position.Parent')"
          min-width="200"
        >
          <template v-slot="{ row }">
            {{ row.parentName }}
          </template>
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="grade"
          :label="tt('Entity.Position.Grade')"
          width="100"
        />
        <el-table-column
          sortable="custom"
          sort-by="enumFlags"
          prop="enumFlagsDesc"
          :label="tt('Entity.Position.EnumFlags')"
          width="150"
        />
        <el-table-column
          sortable="custom"
          prop="remark"
          :label="tt('Entity.Position.Remark')"
          width="280"
        />
        <el-table-column
          fixed="right"
          :label="t('list.operate')"
          class-name="operate"
          :width="cfg.list.operate.width"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              class="view"
              size="small"
              :circle="true"
              :title="t('operate.view')"
              :icon="useRenderIcon('ep:document')"
              @click="viewRow(row)"
            />
            <el-button
              v-if="cfg.btn.edit"
              class="edit"
              size="small"
              :circle="true"
              :title="t('operate.edit')"
              :icon="useRenderIcon('ep:edit')"
              @click="editRow(row)"
            />
            <el-button
              v-if="cfg.btn.delete"
              class="delete"
              size="small"
              :circle="true"
              :title="t('operate.delete')"
              :icon="useRenderIcon('ep:delete')"
              @click="deleteRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <View ref="viewRef" @refresh="getList" />
    <Edit ref="editRef" @refresh="getList" />
  </div>
</template>
