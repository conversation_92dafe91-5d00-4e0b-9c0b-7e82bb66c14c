<script setup lang="ts">
import { ElMessage } from "element-plus";
import { TreeNodeData } from "element-plus/es/components/tree/src/tree.type.mjs";
import { authorizeApi } from "@/api/authorize";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import Node from "element-plus/es/components/tree/src/model/node";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:auth"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "150px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.roleName
    ? `${tt("Entity.Role._Entity")} ${t("role.operate.auth")} -  ${state.roleName}`
    : `${tt("Entity.Role._Entity")} ${t("role.operate.auth")}`;
});

const customNodeClass = (data: TreeNodeData, node: Node) => {
  let css: string = null;
  if (data.enumType === 1) {
    css = "auth-permission";
  } else {
    css = `auth-group-${data.rank}`;

    if (data.children?.some(auth => auth.enumType === 1) === true) {
      css += " auth-group";
    }
  }
  return css;
};

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    auths: [],
    tagAuths: [],
    tabsIndex: "auths"
  },
  tree: {
    props: {
      label: "name",
      class: customNodeClass
    }
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    tabs: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return userStore.hasAnyAuth(["Role:Manage", "Role:Manage:Auth"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  roleName: "",
  auths: cfg.default.auths,
  authedIds: [],
  tagAuths: cfg.default.tagAuths,
  tabsIndex: cfg.default.tabsIndex
});

/**
 * 验证规则
 */
const rules = {
  auths: {},
  authTags: {}
};

/**
 * 存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const treeRef = ref();

/**
 * 初始化组件
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时state
  initState();

  get();
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.roleName = data.name;
  state.auths = data.auths;
  state.authedIds = data.authedIds;
  state.tagAuths = data.tagAuths;
};

/**
 * 获取model数据
 */
const getModel = () => {
  const authedIds = treeRef.value.getCheckedKeys(true);

  const data = {
    id: state.id,
    authedIds,
    tagAuths: state.tagAuths
  };

  return data;
};

/**
 * 初始化state
 */
const initState = () => {
  state.auths = cfg.default.auths;
  state.tagAuths = cfg.default.tagAuths;
  state.tabsIndex = cfg.default.tabsIndex;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.tabs = true;
    authorizeApi
      .GetRoleAuths(state.id, true)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        }
      })
      .finally(() => {
        cfg.loading.tabs = false;
      });
  }
};

/**
 * 授权
 */
const auth = () => {
  cfg.loading.btn = true;

  const data = getModel();

  console.log(data);

  authorizeApi
    .SetRoleAuths(data)
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh("授权成功", "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  if (state.id) {
    auth();
  }
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-tabs v-model="state.tabsIndex" v-loading="cfg.loading.tabs" tab-position="top">
        <el-tab-pane :label="t('role.tab.baseAuth')" name="auths">
          <div class="el-dialog-content">
            <el-tree
              ref="treeRef"
              style="max-width: 90%"
              :data="state.auths"
              node-key="id"
              default-expand-all
              show-checkbox
              check-on-click-node
              :default-checked-keys="state.authedIds"
              :expand-on-click-node="true"
              :props="cfg.tree.props"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane
          v-if="state.tagAuths?.length > 0"
          :label="t('role.tab.tagAuth')"
          name="tagAuths"
        >
          <div class="el-dialog-content">
            <el-form
              ref="formRef"
              label-position="right"
              :label-width="formLabelWidth"
              class="el-dialog-form"
            >
              <el-row v-for="tagAuth in state.tagAuths" :key="tagAuth.authId">
                <el-col :span="24">
                  <el-form-item :label="tagAuth.authName">
                    <el-row>
                      <el-col :span="24">
                        <el-checkbox v-model="tagAuth.allowAllTags">
                          <span style="font-size: 13px; color: #333">{{ t("role.allTags") }}</span>
                        </el-checkbox>
                      </el-col>
                      <el-col v-show="!tagAuth.allowAllTags" :span="24" class="auth-tag-group">
                        <el-checkbox
                          v-for="authTag in tagAuth.authTags"
                          :key="authTag.id"
                          v-model="authTag.checked"
                          :value="authTag.id"
                          class="auth-tag-item"
                          >{{ authTag.name }}
                        </el-checkbox>
                      </el-col>
                    </el-row>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
      <template #footer>
        <div>
          <span style="color: red; margin-right: 50px">{{ t("role.ui.authHint") }}</span>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.tabs"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.save") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="css" scoped>
:deep(.auth-permission .el-tree-node__content) {
  color: #626aef;
}

:deep(.auth-group .el-tree-node__children) {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

:deep(.auth-permission) {
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: 25%;
}

:deep(.auth-tag-group) {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

:deep(.auth-tag-item) {
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: 25%;
}
</style>
