/**
 * 框架改造
 * 重写了整个用户信息的存储、读写逻辑
 * 删除 multipleTabsKey
 * todo: 刷新令牌和自动登录令牌
 */
import { responsiveStorageNameSpace } from "@/config";
import { storageLocal } from "@pureadmin/utils";
import { useUserStoreHook } from "@/store/modules/user";

const authTokenKey = () => getKey("AuthToken");

function getKey(key: string): string {
  return `${responsiveStorageNameSpace()}${key}`;
}

/** 获取`token` */
export function getAuthToken(): string {
  return storageLocal().getItem(authTokenKey());
}

/**
 * @description 设置`authToken`，
 */
export function setAuthToken(authToken: string) {
  storageLocal().setItem(authTokenKey(), authToken);
}

/** 删除`authToken`，以及用户信息 */
export function removeAuthToken() {
  storageLocal().removeItem(authTokenKey());
  useUserStoreHook().REMOVE_USER_INFO();
}

/** 格式化authToken（jwt格式） */
export function formatAuthToken(token: string): string {
  if (token.startsWith("Bearer ")) {
    return token;
  } else {
    return "Bearer " + token;
  }
}

function isAuthByAuthCode(
  userAuths: any, // 用户授权
  authCode: string
): boolean {
  if (!authCode) {
    // 无需授权
    return true;
  } else if (!userAuths) {
    // 未定义用户授权
    return false;
  } else {
    // 用户有授权，需要判断
    return (
      (typeof userAuths === "string" && userAuths === authCode) || // userAuths = "权限"
      (userAuths instanceof Array &&
        userAuths.some(userAuth => isAuthByAuthCode(userAuth, authCode))) || // userAuths = [ userAuth]
      Object.keys(userAuths).includes(authCode) // userAuth = { "权限": xxx }
    );
  }
}

function isAuthByAuthCodeAndAllowAllTags(
  userAuths: any, // 用户授权
  authCode: string,
  allowAllTags: boolean
): boolean {
  if (!authCode) {
    // 无需授权
    return true;
  } else if (!userAuths) {
    // 未定义用户授权
    return false;
  } else {
    // 用户有授权，需要判断
    return (
      (userAuths instanceof Array &&
        userAuths.some(userAuth =>
          isAuthByAuthCodeAndAllowAllTags(userAuth, authCode, allowAllTags)
        )) || // userAuths = [ userAuth]
      (Object.keys(userAuths).includes(authCode) && // userAuth = {"权限": xxx }
        ((typeof userAuths[authCode] === "boolean" && // userAuth = {"权限": boolean }
          userAuths[authCode] === allowAllTags) ||
          (allowAllTags === false &&
            userAuths[authCode] instanceof Array &&
            userAuths[authCode].length === 0))) // 当 allowAllTags === false 时 userAuth = { "权限": [] } 也满足条件
    );
  }
}

function isAuthByAuthCodeAndAuthTagCode(
  userAuths: any, // 用户授权
  authCode: string,
  authTagCode: string
): boolean {
  if (!authCode) {
    // 无需授权
    return true;
  } else if (!userAuths) {
    // 未定义用户授权
    return false;
  } else {
    // 用户有授权，需要判断
    return (
      (userAuths instanceof Array &&
        userAuths.some(userAuth =>
          isAuthByAuthCodeAndAuthTagCode(userAuth, authCode, authTagCode)
        )) || // userAuths = [ userAuth]
      (Object.keys(userAuths).includes(authCode) && // userAuth = {"权限"：xxx }
        ((typeof userAuths[authCode] === "boolean" && userAuths[authCode] === true) || // userAuth = { "权限": true }
          (typeof userAuths[authCode] === "string" && userAuths[authCode] === authTagCode) || // userAuth = {  "权限":"标签" }
          (userAuths[authCode] instanceof Array && userAuths[authCode].includes(authTagCode)))) // userAuth = { 权限": ["标签"] }
    );
  }
}

function isAuthByAuthCodeAndAuthTagCodes(
  userAuths: any, // 用户授权
  authCode: string,
  authTagCodes: Array<string>
): boolean {
  if (!authCode) {
    // 无需授权
    return true;
  } else if (!userAuths) {
    // 未定义用户授权
    return false;
  } else {
    // 用户有授权，需要判断
    return (
      (userAuths instanceof Array &&
        userAuths.some(userAuth =>
          isAuthByAuthCodeAndAuthTagCodes(userAuth, authCode, authTagCodes)
        )) || // userAuths = [ userAuth]
      (Object.keys(userAuths).includes(authCode) && // userAuth = { "权限": xxx }
        ((typeof userAuths[authCode] === "boolean" && userAuths[authCode] === true) || // userAuth = { "权限": true }
          (userAuths[authCode] instanceof Array &&
            authTagCodes.every(authTagCode => userAuths[authCode].includes(authTagCode))))) // userAuth = { 权限": ["标签"] }, 需检验每一个标签都存在
    );
  }
}

export function isAuth(
  userAuths: any, // 用户授权
  auth:
    | string // auth = "权限"
    | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
): boolean {
  if (!auth) {
    // 无需授权
    return true;
  } else if (!userAuths) {
    // 未定义用户授权
    return false;
  } else {
    if (typeof auth === "string") {
      // auth = "权限"
      return isAuthByAuthCode(userAuths, auth);
    } else {
      return Object.keys(auth).every(authCode => {
        if (auth[authCode] === undefined || auth[authCode] === null) {
          // auth = { "权限", nulll | undefined }
          return isAuthByAuthCode(userAuths, authCode);
        } else if (typeof auth[authCode] === "boolean") {
          // auth = { "权限": allowAllTags }
          return isAuthByAuthCodeAndAllowAllTags(userAuths, authCode, auth[authCode]);
        } else if (typeof auth[authCode] === "string") {
          // auth = { "权限": "标签" }
          return isAuthByAuthCodeAndAuthTagCode(userAuths, authCode, auth[authCode]);
        } else if (auth[authCode] instanceof Array) {
          // auth = { "权限": "标签" }
          return isAuthByAuthCodeAndAuthTagCodes(userAuths, authCode, auth[authCode]);
        } else {
          // auth 定义错误
          return false;
        }
      });
    }
  }
}

export function isAnyAuth(
  userAuths: any, // 用户授权
  auths:
    | string // auths = "权限"
    | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
    | Array<
        // auths = [auth]
        | string // auth = "权限"
        | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
      >
): boolean {
  if (!auths) {
    // 无需授权
    return true;
  } else if (!userAuths) {
    // 未定义用户授权
    return false;
  } else {
    if (auths instanceof Array) {
      // auths = [auth], 数组
      if (auths.length === 0) {
        // 无需授权
        return true;
      } else {
        return auths.some(auth => {
          return isAuth(userAuths, auth);
        });
      }
    } else {
      // auths = auth, 非数组
      const auth = auths;
      return isAuth(userAuths, auth);
    }
  }
}

export function isAllAuth(
  userAuths: any, // 用户授权
  auths:
    | string // auths = "权限"
    | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
    | Array<
        // auths = [auth]
        | string // auth = "权限"
        | Record<string, undefined | null | boolean | string | Array<string>> // auth = { "权限": undefined | null | allowAllTags | "标签" | ["标签"] }, 对象内所有权限均需满足
      >
): boolean {
  if (!auths) {
    // 无需授权
    return true;
  } else if (!userAuths) {
    // 未定义用户授权
    return false;
  } else {
    if (auths instanceof Array) {
      // auths = [auth], 数组
      if (auths.length === 0) {
        // 无需授权
        return true;
      } else {
        return auths.every(auth => {
          return isAuth(userAuths, auth);
        });
      }
    } else {
      // auths = auth, 非数组
      const auth = auths;
      return isAuth(userAuths, auth);
    }
  }
}

export const allowEdit = (model: Record<string | "enumEditFlags", any>, editFlags) => {
  return (
    model.enumEditFlags &&
    typeof model.enumEditFlags === "number" &&
    (model.enumEditFlags === -1 || (model.enumEditFlags & editFlags) !== 0)
  );
};
