/**
 * 框架改造
 *
 * 新增 decodeCycleJson
 * todo: encodeCycleJson
 */

const decodeCycleJson = (jsonObj: any) => {
  if (typeof jsonObj === "string") {
    jsonObj = JSON.parse(jsonObj);
  }

  const objById: Record<string, any> = {}; // all objects by id
  const refs = []; // references to objects that could not be resolved

  const recurse = (obj: any, prop?: string, parent?: any): any => {
    if (typeof obj !== "object" || !obj) {
      // a primitive value
      return obj;
    }

    if (obj.hasOwnProperty("$id")) {
      const id = obj.$id;
      delete obj.$id;
      if (obj.hasOwnProperty("$values") && obj.$values instanceof Array) {
        // an array
        obj = obj.$values.map(recurse);
      } else {
        // a plain object
        Object.keys(obj).forEach(prop => {
          obj[prop] = recurse(obj[prop], prop, obj);
        });

        objById[id] = obj;
      }
    } else if (obj.hasOwnProperty("$ref")) {
      // a reference
      const ref = obj.$ref;
      if (ref in objById) {
        return objById[ref];
      }
      // else we have to make it lazy:
      if (parent) {
        refs.push([parent, prop, ref]);
      }
      return;
    }

    return obj;
  };

  jsonObj = recurse(jsonObj);

  for (let i = 0; i < refs.length; i++) {
    // resolve previously unknown references
    var ref = refs[i];
    ref[0][ref[1]] = objById[refs[2]];
    // Notice that this throws if you put in a reference at top-level
  }
  return jsonObj;
};

const encodeCycleJson = (jsonObj: any) => {
  if (typeof jsonObj === "string") {
    jsonObj = JSON.parse(jsonObj);
  }
  // todo
  return jsonObj;
};

export { decodeCycleJson, encodeCycleJson };
