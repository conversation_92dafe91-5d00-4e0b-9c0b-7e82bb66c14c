<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "EnumFlagsCheckbox"
});

const emit = defineEmits(["change"]);

const model = defineModel("modelValue", {
  type: Number,
  default: 0,
  get: val => {
    let enumValue = 0;

    props.enums.forEach(enumInfo => {
      enumValue |= enumInfo.value;
    });

    if (state.orgValue !== val || state.orgEnumsValue !== enumValue) {
      state.orgValue = val;
      state.orgEnumsValue = enumValue;
      setValues(val);
    }
    return val;
  }
});

const state = reactive({
  values: [],
  orgValue: 0,
  extValue: 0,
  orgEnumsValue: 0
});

const setValues = (value: number) => {
  const newValues = [];
  state.extValue = value;

  props.enums.forEach(enumInfo => {
    if ((enumInfo.value & value) !== 0) {
      newValues.push(enumInfo.value);
      state.extValue &= ~enumInfo.value;
    }
  });

  state.values = newValues;
};

const setModel = (values: Array<number>) => {
  let newValue = state.extValue;
  values.forEach((flag: number) => {
    newValue |= flag;
  });
  model.value = newValue;
};

const props = defineProps({
  enumType: {
    required: true,
    type: String
  },
  enums: {
    required: true,
    type: Array<any>
  },
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 可被勾选的 checkbox 的最小数量
  min: {
    type: Number
  },
  // 可被勾选的 checkbox 的最大数量
  max: {
    type: Number
  },
  //是否显示边框
  border: {
    type: Boolean,
    default: false
  },
  //是否禁用选项
  allowDisableOption: {
    type: Boolean,
    default: true
  }
});

const change = newValues => {
  setModel(newValues);
  emit("change", newValues);
};
</script>

<template>
  <el-checkbox-group
    v-model="state.values"
    :disabled="disabled"
    :min="min"
    :max="max"
    @change="change"
  >
    <el-checkbox
      v-for="item in props.enums"
      :key="item"
      :label="props.enumType ? `${tt('Enums.' + enumType + '.' + item.name)}` : item.text"
      :value="item.value"
      :border="border"
      :disabled="props.allowDisableOption && item.disabled"
    />
  </el-checkbox-group>
  <el-input-number v-show="false" v-model="model" />
</template>
