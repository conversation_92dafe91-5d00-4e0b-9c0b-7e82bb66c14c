export default {
  path: "/masterData",
  name: "MasterData",
  meta: {
    icon: "mdi:database-edit-outline",
    title: "主数据维护",
    alwaysShow: true,
    rank: 800
  },
  children: [
    {
      path: "/masterData/basic",
      name: "MasterData-Basic",
      meta: {
        title: "基础主数据",
        alwaysShow: true
      },
      children: [
        {
          path: "/masterData/basic/organization",
          name: "organization:query",
          component: () => import("@/views/masterData/basic/organization/query.vue"),
          meta: {
            title: "组织架构",
            auths: [
              "Organization:Query",
              "Organization:Manage",
              "Organization:Manage:Department",
              "Organization:Manage:Department:Add",
              "Organization:Manage:Department:Edit",
              "Organization:Manage:Department:Delete",
              "Organization:Manage:Department:Move",
              "Organization:Manage:Department:CostCenter",
              "Organization:Manage:Department:Valid",
              "Organization:Manage:Station",
              "Organization:Manage:Station:Add",
              "Organization:Manage:Station:Edit",
              "Organization:Manage:Station:Delete",
              "Organization:Manage:Station:Employee",
              "Organization:Manage:Station:Valid"
            ]
          }
        },
        {
          path: "/masterData/basic/subCompany",
          name: "subCompany:query",
          component: () => import("@/views/masterData/basic/subCompany/query.vue"),
          meta: {
            title: "分公司",
            auths: [
              "SubCompany:Query",
              "SubCompany:Manage",
              "SubCompany:Manage:Add",
              "SubCompany:Manage:Edit",
              "SubCompany:Manage:Delete",
              "SubCompany:Manage:Valid"
            ]
          }
        },
        {
          path: "/masterData/basic/costCenter",
          name: "costCenter:query",
          component: () => import("@/views/masterData/basic/costCenter/query.vue"),
          meta: {
            title: "成本中心",
            auths: [
              "CostCenter:Query",
              "CostCenter:Manage",
              "CostCenter:Manage:Add",
              "CostCenter:Manage:Edit",
              "CostCenter:Manage:Delete",
              "CostCenter:Manage:Valid"
            ]
          }
        },
        {
          path: "/masterData/basic/position",
          name: "position:query",
          component: () => import("@/views/masterData/basic/position/query.vue"),
          meta: {
            title: "职位",
            auths: [
              "Position:Query",
              "Position:Manage",
              "Position:Manage:Add",
              "Position:Manage:Edit",
              "Position:Manage:Delete"
            ]
          }
        }
      ]
    },
    {
      path: "/masterData/business",
      name: "MasterData-Business",
      meta: {
        title: "业务主数据",
        alwaysShow: true
      },
      children: [
        {
          path: "/masterData/business/bizDict",
          name: "bizDict:query",
          component: () => import("@/views/masterData/business/bizDict/query.vue"),
          meta: {
            title: "业务字典",
            auths: [
              "BizDict:Query",
              "BizDict:Manage",
              "BizDict:Manage:Add",
              "BizDict:Manage:Edit",
              "BizDict:Manage:Delete"
            ]
          }
        }
      ]
    }
  ]
} satisfies RouteConfigsTable;
