import { useHttpApi } from "./libs/httpApi";

const controller = "Selector";

const api = useHttpApi(controller);

export const selectorApi = {
  GetEnumInfos(data: any, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetEnumInfos", data, config);
  },
  GetCultures(config?: ApiRequestConfig) {
    return api.get<BizResult>("GetCultures", null, config);
  },
  GetDicts(parentCode: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetDicts", { parentCode }, config);
  },
  GetPositionSelectors() {
    return api.get<BizResult>("GetPositionSelectors");
  },
  GetSubCompanySelectors() {
    return api.get<BizResult>("GetSubCompanySelectors");
  },
  GetCostCenterSelectors() {
    return api.get<BizResult>("GetCostCenterSelectors");
  },
  QueryEmployeeSelector(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryEmployeeSelector", data, config);
  },
  QueryStationSelector(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryStationSelector", data, config);
  },
  QueryPositionSelector(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryPositionSelector", data, config);
  },
  QueryDepartmentSelector(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryDepartmentSelector", data, config);
  },
  GetStationSelectors() {
    return api.get<BizResult>("GetStationSelectors");
  },
  QueryCostCenterSelectors(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryCostCenterSelectors", data, config);
  }
};
