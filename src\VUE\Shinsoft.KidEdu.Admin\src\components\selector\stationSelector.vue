<script setup lang="ts">
import { selectorApi } from "@/api/selector";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useEnumStoreHook } from "@/store/modules/enum";
import { getColumnOrder, getDefaultOrder, resetFilter } from "@/utils/table";
import { Sort } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "station:selector"
});

const props = defineProps({
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  height: {
    default: 480
  },
  allowedValids: {
    default: [true]
  }
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: [],
    total: 0,
    filter: {
      keywords: "",
      valids: props.allowedValids,
      pageSize: 10
    }
  },
  filter: {
    gutter: 3,
    span: 4,
    valids: [true, false]
  },
  list: {
    defaultSort: { prop: "name", order: "ascending" } as Sort
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 存储
 */
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const filterRef = ref();
const listRef = ref();

/**
 * 初始化组件
 */
const init = (init?: boolean) => {
  init ??= true;
  listRef.value.clearSelection();
  if (init) {
    // 防止之前打开的数据残留，因此初始化时赋予state默认值
    initState();
    initFilter();
    query();
  }
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  resetFilter(filter, cfg.default.filter);
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  if (props.allowedValids?.length > 0) {
    cfg.filter.valids = props.allowedValids;
  }
};

/**
 * 获取列表数据
 */
const getList = () => {
  cfg.loading.list = true;
  selectorApi
    .QueryStationSelector(filter)
    .then(res => {
      if (res.success) {
        state.datas = res.datas;
        state.total = res.total;
        filter.pageIndex = res.pageIndex;
      }
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filterRef.value.validate((valid, fields) => {
    if (valid) {
      filter.pageIndex = 1;

      if (props.allowedValids?.length > 0 && (!filter.valids || filter.valids.length === 0)) {
        filter.valids = props.allowedValids;
      }

      getList();
    }
  });
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

const rowClick = (row, column) => {
  if (!props.multiple) {
    const selection = listRef.value.getSelectionRows();
    selection.forEach(p => {
      if (p.id !== row.id) {
        listRef.value.toggleRowSelection(p);
      }
    });
  }

  listRef.value.toggleRowSelection(row);
};

const select = (selection, row) => {
  if (!props.multiple) {
    selection.forEach(p => {
      if (p.id !== row.id) {
        listRef.value.toggleRowSelection(p);
      }
    });
  }
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
  listRef.value.clearSelection();
};

/**
 * 清空
 */
const clear = () => {
  initState();
};

const getSelectionRows = (): Array<any> => {
  return listRef.value.getSelectionRows();
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  init,
  clear,
  getSelectionRows
});
</script>

<template>
  <div class="filter-container">
    <el-form
      ref="filterRef"
      v-loading="cfg.loading.filter"
      class="filter-form"
      :rules="rules.filter"
      :model="filter"
    >
      <el-row :gutter="cfg.filter.gutter" type="flex">
        <el-col :span="cfg.filter.span">
          <el-form-item>
            <el-input
              v-model="filter.keywords"
              clearable
              :placeholder="t('filter.keywords')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-form-item>
        </el-col>
        <el-col :span="cfg.filter.span">
          <el-form-item>
            <el-select
              v-model="filter.valids"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              :placeholder="tt('Entity.Station.Valid')"
            >
              <el-option
                v-for="item in cfg.filter.valids"
                :key="item ? 1 : 0"
                :label="item ? t('shinsoft.valid') : t('shinsoft.valid')"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="cfg.filter.span">
          <el-button
            class="query"
            :loading="cfg.loading.list"
            :icon="useRenderIcon('ep:search')"
            @click="query"
          >
            {{ t("operate.query") }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="list-container">
    <el-table
      ref="listRef"
      v-loading="cfg.loading.list"
      :data="state.datas"
      :height="props.height"
      :default-sort="cfg.list.defaultSort"
      row-key="id"
      stripe
      border
      class-name="list"
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="rowClick"
      @select="select"
    >
      <template #empty>
        <NoDatas />
      </template>

      <el-table-column
        fixed
        type="selection"
        width="40"
        align="center"
        :label-class-name="props.multiple ? '' : 'header-hidden'"
      />
      <el-table-column
        fixed
        :label="t('list.no')"
        :show-overflow-tooltip="true"
        width="60"
        align="center"
      >
        <template v-slot="scope">
          <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        fixed
        sortable="custom"
        prop="name"
        :label="tt('Entity.Station.Name')"
        width="150"
      />
      <el-table-column
        sortable="custom"
        prop="departmentName"
        :label="tt('Entity.Department._Entity')"
        width="200"
      />
      <el-table-column
        sortable="custom"
        prop="positionName"
        :label="tt('Entity.Position._Entity')"
        width="150"
      />
      <el-table-column
        sortable="custom"
        prop="positionGrade"
        :label="tt('Entity.Position.Grade')"
        width="100"
        align="right"
      />
      <el-table-column
        sortable="custom"
        prop="startDate"
        :label="tt('Entity.Station.StartDate')"
        width="120"
        align="center"
      />
      <el-table-column
        sortable="custom"
        prop="endDate"
        :label="tt('Entity.Station.EndDate')"
        width="120"
        align="center"
      />
      <el-table-column
        sortable="custom"
        sort-by="valid"
        :label="tt('Entity.Station.Valid')"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-icon v-if="row.valid" size="18px"><icon-ep-select /></el-icon>
          <el-icon v-else size="18px"><icon-ep-close-bold /></el-icon>
        </template>
      </el-table-column>
      <el-table-column
        sortable="custom"
        prop="remark"
        :label="tt('Entity.Station.Remark')"
        min-width="200"
      />
      <el-table-column
        sortable="custom"
        prop="parentName"
        :label="tt('Entity.Station.Parent')"
        width="150"
      />
    </el-table>
    <pagination v-model:filter="filter" :total="state.total" @change="getList" />
  </div>
</template>
