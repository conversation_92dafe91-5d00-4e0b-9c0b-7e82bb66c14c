buttons:
  pureLoginOut: 退出系统
  pureLogin: 登录
  pureOpenSystemSet: 打开系统配置
  pureReload: 重新加载
  pureCloseCurrentTab: 关闭当前标签页
  pureCloseLeftTabs: 关闭左侧标签页
  pureCloseRightTabs: 关闭右侧标签页
  pureCloseOtherTabs: 关闭其他标签页
  pureCloseAllTabs: 关闭全部标签页
  pureContentFullScreen: 内容区全屏
  pureContentExitFullScreen: 内容区退出全屏
  pureClickCollapse: 点击折叠
  pureClickExpand: 点击展开
  pureConfirm: 确认
  pureSwitch: 切换
  pureClose: 关闭
  pureBackTop: 回到顶部
  pureOpenText: 开
  pureCloseText: 关
search:
  pureTotal: 共
  pureHistory: 搜索历史
  pureCollect: 收藏
  pureDragSort: （可拖拽排序）
  pureEmpty: 暂无搜索结果
  purePlaceholder: 搜索菜单（支持拼音搜索）
panel:
  pureSystemSet: 系统配置
  pureCloseSystemSet: 关闭配置
  pureClearCacheAndToLogin: 清空缓存并返回登录页
  pureClearCache: 清空缓存
  pureOverallStyle: 整体风格
  pureOverallStyleLight: 浅色
  pureOverallStyleLightTip: 清新启航，点亮舒适的工作界面
  pureOverallStyleDark: 深色
  pureOverallStyleDarkTip: 月光序曲，沉醉于夜的静谧雅致
  pureOverallStyleSystem: 自动
  pureOverallStyleSystemTip: 同步时光，界面随晨昏自然呼应
  pureThemeColor: 主题色
  pureLayoutModel: 导航模式
  pureVerticalTip: 左侧菜单，亲切熟悉
  pureHorizontalTip: 顶部菜单，简洁概览
  pureMixTip: 混合菜单，灵活多变
  pureStretch: 页宽
  pureStretchFixed: 固定
  pureStretchFixedTip: 紧凑页面，轻松找到所需信息
  pureStretchCustom: 自定义
  pureStretchCustomTip: 最小1280、最大1600
  pureTagsStyle: 页签风格
  pureTagsStyleSmart: 灵动
  pureTagsStyleSmartTip: 灵动标签，添趣生辉
  pureTagsStyleCard: 卡片
  pureTagsStyleCardTip: 卡片标签，高效浏览
  pureTagsStyleChrome: 谷歌
  pureTagsStyleChromeTip: 谷歌风格，经典美观
  pureInterfaceDisplay: 界面显示
  pureGreyModel: 灰色模式
  pureWeakModel: 色弱模式
  pureHiddenTags: 隐藏标签页
  pureHiddenFooter: 隐藏页脚
  pureMultiTagsCache: 页签持久化
status:
  pureLoad: 加载中...
  pureMessage: 消息
  pureNotify: 通知
  pureTodo: 待办
  pureNoMessage: 暂无消息
  pureNoNotify: 暂无通知
  pureNoTodo: 暂无待办
login:
  pureUsername: 账号
  purePassword: 密码
  pureLogin: 登录
  pureLoginSuccess: 登录成功
  pureLoginFail: 登录失败
  pureUsernameReg: 请输入账号
  purePassWordReg: 请输入密码
  purePassWordRuleReg: 密码格式应为8-18位数字、字母、符号的任意两种组合
titles:
  info: 信息
  warning: 警告
  error: 错误
shinsoft:
  select: 请选择
  accountSettings: 账户设置
  delegate: 代理员工
  editPwd: 修改密码
  setDelegate: 设置代理
  current: 当前
  delegating: 代理中
  delegateAble: 可代理
  basicInfo: 基本信息
  valid: 有效
  invalid: 无效
filter:
  keywords: 关键字
  startDate: 开始时间
  endDate: 结束时间
list:
  no: 序号
  operate: 操作
  noDatas: 没有数据
  onDuty: 是否在岗
operate:
  ok: 确认
  cancel: 取消
  query: 查询
  view: 查看
  close: 关闭
  add: 新增
  edit: 编辑
  delete: 删除
  save: 保存
  submit: 提交
  print: 打印
  approve: 通过
  reject: 拒绝
  append: 添加
  select: 选择
  saveDraft: 暂存
  more: 更多
  setMajorStation: 设置主岗
  setDefaultCostCenter: 设置默认成本中心
  title:
    deleteConfirm: 删除确认
    operateConfirm: 操作确认
  message:
    success: 操作成功
    noCheckedItems: 请选择要添加的项
  confirm:
    delete: "请确认是否要执行删除操作?"
    deleteHint: "提示：删除后将不可恢复!"
    valid: "请确认是否要执行有效性操作?"
    validHint: "提示：是否确认操作!"
  yes: 是
  no: 否
menus:
  pureHome: 首页
  pureLogin: 登录
  pureAbnormal: 异常页面
  pureFourZeroFour: "404"
  pureFourZeroOne: "403"
  pureFive: "500"
  purePermission: 权限管理
  purePermissionPage: 页面权限
  purePermissionButton: 按钮权限
  authorize:
    _group: 权限管理
    employee: 员工管理
    role: 角色管理
  app:
    _group: 单据查询,
    edit: 申请
    view: 审阅
    audit: 审批
    editLeaveConfirmText: 即将离开当前页面，未保存的数据将会丢失
app:
  editTitle: 编辑申请单
  viewTitle: 查看申请单
  errorTitle: 申请单错误
  schemaError: 页面结构数据错误
 
role:
  allTags: 全部
  operate:
    auth: 授权
    member: 成员
  tab:
    auth: 授权
    member: 成员
    employee: 员工
    baseAuth: 基本授权
    tagAuth: 内容授权
  dialog:  
    roleMember: 角色成员
  ui:
    authHint: 请完成所有标签页内的授权设置，然后再保存
announcement:
  title: 公告
workflow:
  operate:
    history: 历史版本
    design: 流程设置
  template:
    name: 名称
    code: 编号

