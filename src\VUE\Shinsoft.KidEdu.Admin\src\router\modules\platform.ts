export default {
  path: "/platform",
  redirect: "/platform/company",
  meta: {
    icon: "ep:office-building",
    title: "平台管理",
    alwaysShow: true,
    rank: 100
  },
  children: [
    {
      path: "/platform/company",
      name: "platform:company:query",
      component: () => import("@/views/platform/company/query.vue"),
      meta: {
        title: "公司管理",
        auths: "Platform:Company:Config"
      }
    },
    {
      path: "/platform/i18n",
      name: "platform:i18n:query",
      component: () => import("@/views/platform/i18n/query.vue"),
      meta: {
        title: "多语言文本管理",
        auths: "Platform:I18n:Manage"
      }
    }
  ]
} satisfies RouteConfigsTable;
