<script setup lang="ts">
import { OfficeBuilding } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:baseInfo"
});

const model = defineModel<Record<string, any>>("modelValue", {
  required: true,
  default: {}
});
</script>

<template>
  <div>
    <el-row :gutter="10" class="stationInfo">
      <el-col class="station-title">
        <el-icon size="15" span="24"><OfficeBuilding /></el-icon>基础信息
      </el-col>
      <el-col class="product-content" :span="8"
        >{{ tt("Entity.SubCompany.Name") }}：{{ model.name }}</el-col
      >
      <el-col class="product-content" :span="8"
        >{{ tt("Entity.Company.EnumType") }}：{{ model.enumTypeDesc }}<span
      /></el-col>
      <el-col class="product-content" :span="8"
        >{{ tt("Entity.Department.Parent") }}：{{ model.parentName }}</el-col
      >
    </el-row>
  </div>
</template>
