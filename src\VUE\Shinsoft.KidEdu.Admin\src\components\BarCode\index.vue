<script setup lang="ts">
import JsBarcode from "jsbarcode";

var props = defineProps({
  height: {
    type: Number,
    default: 60
  },
  code: {
    type: String,
    require: true
  }
});

onMounted(() => {
  JsBarcode("#barCode", props.code, {
    format: "CODE128", // 指定条形码的格式
    lineColor: "#444", // 条形码颜色
    width: 1.5, // 条的宽度
    height: props.height, // 条形码的高度
    displayValue: true // 是否显示文本
  });
});
</script>
<template>
  <div>
    <div class="code-content">
      <svg id="barCode" />
    </div>
  </div>
</template>
<style lang="scss"></style>
