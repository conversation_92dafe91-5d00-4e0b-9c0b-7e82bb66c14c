import { cdn } from "./cdn";
import vue from "@vitejs/plugin-vue";
import { pathResolve } from "./utils";
import { viteBuildInfo } from "./info";
import svgLoader from "vite-svg-loader";
import type { PluginOption } from "vite";
import checker from "vite-plugin-checker";
import vueJsx from "@vitejs/plugin-vue-jsx";
import Inspector from "vite-plugin-vue-inspector";
import { configCompressPlugin } from "./compress";
import removeNoMatch from "vite-plugin-router-warn";
import { visualizer } from "rollup-plugin-visualizer";
import removeConsole from "vite-plugin-remove-console";
import { themePreprocessorPlugin } from "@pureadmin/theme";
import VueI18nPlugin from "@intlify/unplugin-vue-i18n/vite";
import { genScssMultipleScopeVars } from "../src/layout/theme";
import { vitePluginFakeServer } from "vite-plugin-fake-server";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
// import {
//   ElementPlusResolver,
//   VueUseComponentsResolver,
//   VueUseDirectiveResolver
// } from "unplugin-vue-components/resolvers";
import Icons from "unplugin-icons/vite";
// icon 自动引入解析器
import IconsResolver from "unplugin-icons/resolver";
// icon 加载 loader
// import { FileSystemIconLoader } from "unplugin-icons/loaders";

export function getPluginsList(
  VITE_CDN: boolean,
  VITE_COMPRESSION: ViteCompression
): PluginOption[] {
  const lifecycle = process.env.npm_lifecycle_event;
  return [
    vue(),
    AutoImport({
      imports: ["vue", "vue-router"],
      dts: "types/auto-imports.d.ts" // 使用typescript，需要指定生成对应的d.ts文件或者设置为true,生成默认导入d.ts文件
    }),
    Components({
      //imports指定组件所在目录，默认为src/components
      dirs: [], // ["src/components", "src/views"],
      //需要去解析的文件
      // include: [/\.vue$/, /\.vue\?vue/, /\.md$/],
      resolvers: [
        // ElementPlusResolver(),
        // VueUseComponentsResolver(),
        // VueUseComponentsResolver(),
        // VueUseDirectiveResolver(),
        IconsResolver({
          // 自动引入的Icon组件统一前缀，默认为icon，设置false为不需要前缀
          prefix: "icon",
          // 当图标集名字过长时，可使用集合别名
          alias: {
            system: "system-uicons"
          }
        })
      ],
      dts: "types/components.d.ts"
    }),
    Icons({
      compiler: "vue3", // 指定编译器
      autoInstall: true // 自动安装
    }),
    // jsx、tsx语法支持
    vueJsx(),
    VueI18nPlugin({
      jitCompilation: false,
      include: [pathResolve("../locales/**")]
    }),
    checker({
      typescript: true,
      vueTsc: true,
      eslint: {
        lintCommand: `eslint ${pathResolve("../{src,mock,build}/**/*.{vue,js,ts,tsx}")}`,
        useFlatConfig: true
      },
      terminal: false,
      enableBuild: false
    }),
    // 按下Command(⌘)+Shift(⇧)，然后点击页面元素会自动打开本地IDE并跳转到对应的代码位置
    Inspector(),
    viteBuildInfo(),
    /**
     * 开发环境下移除非必要的vue-router动态路由警告No match found for location with path
     * 非必要具体看 https://github.com/vuejs/router/issues/521 和 https://github.com/vuejs/router/issues/359
     * vite-plugin-router-warn只在开发环境下启用，只处理vue-router文件并且只在服务启动或重启时运行一次，性能消耗可忽略不计
     */
    removeNoMatch(),
    // mock支持
    vitePluginFakeServer({
      logger: false,
      include: "mock",
      infixName: false,
      enableProd: true
    }),
    // 自定义主题
    themePreprocessorPlugin({
      scss: {
        multipleScopeVars: genScssMultipleScopeVars(),
        extract: true
      }
    }),
    // svg组件化支持
    svgLoader(),
    VITE_CDN ? cdn : null,
    configCompressPlugin(VITE_COMPRESSION),
    // 线上环境删除console
    removeConsole({ external: ["src/assets/iconfont/iconfont.js"] }),
    // 打包分析
    lifecycle === "report"
      ? visualizer({ open: true, brotliSize: true, filename: "report.html" })
      : (null as any)
  ];
}
