<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { useResizeObserver } from "@vueuse/core";
import { homeApi } from "@/api/home";
import { Message } from "@element-plus/icons-vue";
import View from "./viewcol.vue";
import { goToRoute } from "@/router/utils";

import { useI18n } from "vue-i18n";
const { t } = useI18n();
const tt = t;

defineOptions({
  name: "announcementHome:query"
});

/**
 * 基本配置定义
 */
const userStore = useUserStoreHook();
const cfg = reactive({
  filter: {
    gutter: 10,
    span: 12
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});
// var observer;
var tableHeight = ref(300);
onMounted(() => {
  let cardDom = document.getElementById("announcement");
  //计算列表高度
  useResizeObserver(cardDom, entries => {
    const entry = entries[0];
    const { width, height } = entry.contentRect;
    tableHeight.value = height - 70;
  });
});

onUnmounted(() => {
  // observer.disconnect();
});
// 路由实例
const router = useRouter();

// 跳转到“更多”页面
const goToMorePage = () => {
  // router.push({ name: "announcement:view" });
  const route = {
    path: "/announcement/more"
  };
  goToRoute(route);
};

const viewRef = ref();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  queryAnnouncement();
};

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  homeApi
    .QueryAnnouncement(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const queryAnnouncement = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: "",
  dates: []
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div class="content-container">
    <el-card id="announcement" shadow="always">
      <template #header>
        <div class="announcement-header">
          <div class="announcement-title">
            <el-icon class="announcement-icon"><Message /></el-icon>
            {{ tt("Entity.Announcement._Entity") }}
          </div>
          <el-link :underline="false" class="more-link" @click="goToMorePage"
            >{{ t("operate.more") }} >></el-link
          >
        </div>
      </template>
      <el-table
        v-if="state.datas.some(item => item.isShow)"
        v-loading="cfg.loading.list"
        :data="state.datas"
        stripe
        class-name="list"
        style="width: 100%"
        :max-height="tableHeight"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column fixed prop="subject" :label="tt('Entity.Announcement.Subject')">
          <template #default="scope">
            <span class="clickable" @click="viewRow(scope.row)">
              {{ scope.row.subject }}
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="publishTime"
          header-align="center"
          width="150"
          :label="tt('Entity.Announcement.StartTime')"
        >
          <template #default="scope">
            <span>
              {{ scope.row.startTime }}
            </span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <View ref="viewRef" @refresh="getList" />
  </div>
</template>

<style scoped>
/* 新增 .clickable 类，用于提示行的点击 */
.clickable {
  cursor: pointer;
  color: #409eff; /* 默认文字颜色 */
}

.clickable:hover {
  text-decoration: underline;
  color: #66b1ff; /* 鼠标悬停时颜色变化 */
}

.content-container {
  height: 100%;
}
#announcement {
  width: 100%;
  height: 45vh;
}

:deep #announcement .el-card__header {
  padding: calc(var(--el-card-padding) - 10px) !important;
}
.announcement-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
}
.announcement-title {
  display: flex;
  align-items: center;
  color: rgb(72, 178, 220);
  font-size: 1.5em;
  font-family: "Times New Roman", Times, serif;
  font-weight: bold;
}

.announcement-title .announcement-icon {
  font-size: 40px;
  margin-right: 8px;
}

.more-link {
  color: rgb(72, 178, 220);
  font-size: 0.9em;
  font-family: "Times New Roman", Times, serif;
  cursor: pointer;
  margin-right: 10px;
}

:deep(.el-card__body) {
  padding: 0;
}
</style>
