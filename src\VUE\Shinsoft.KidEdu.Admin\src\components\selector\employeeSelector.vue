<script setup lang="ts">
import { selectorApi } from "@/api/selector";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useEnumStoreHook } from "@/store/modules/enum";
import { getColumnOrder, getDefaultOrder, resetFilter } from "@/utils/table";
import { Sort } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "employee:selector"
});

const props = defineProps({
  // 是否多选
  multiple: {
    type: Boolean,
    default: true
  },
  height: {
    default: 480
  },
  allowedEmployeeStatus: {
    default: [1]
  }
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: [],
    total: 0,
    filter: {
      keywords: "",
      genders: [],
      status: props.allowedEmployeeStatus,
      pageSize: 10
    }
  },
  enums: {
    gender: [],
    employeeStatus: []
  },
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    defaultSort: { prop: "displayName", order: "ascending" } as Sort
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 存储
 */
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const filterRef = ref();
const listRef = ref();

/**
 * 初始化组件
 */
const init = (init?: boolean) => {
  init ??= true;
  listRef.value.clearSelection();

  if (init) {
    // 防止之前打开的数据残留，因此初始化时赋予state默认值
    initState();
    initFilter().then(() => {
      query();
    });
  }
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  resetFilter(filter, cfg.default.filter);
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;

  const initGender = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("Gender").then(enumInfos => {
        cfg.enums.gender = enumInfos;
        resolve();
      });
    });
  };

  const initEmployeeStatus = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("EmployeeStatus").then(enumInfos => {
        cfg.enums.employeeStatus = enumInfos.filter(enumInfo =>
          props.allowedEmployeeStatus.includes(enumInfo.value)
        );
        resolve();
      });
    });
  };

  const allInits = [initGender(), initEmployeeStatus()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据
 */
const getList = () => {
  cfg.loading.list = true;
  selectorApi
    .QueryEmployeeSelector(filter)
    .then(res => {
      if (res.success) {
        state.datas = res.datas;
        state.total = res.total;
        filter.pageIndex = res.pageIndex;
      }
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filterRef.value.validate((valid, fields) => {
    if (valid) {
      filter.pageIndex = 1;

      if (
        props.allowedEmployeeStatus?.length > 0 &&
        (!filter.status || filter.status.length === 0)
      ) {
        filter.status = props.allowedEmployeeStatus;
      }
      getList();
    }
  });
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

const rowClick = (row, column) => {
  if (!props.multiple) {
    const selection = listRef.value.getSelectionRows();
    selection.forEach(p => {
      if (p.id !== row.id) {
        listRef.value.toggleRowSelection(p);
      }
    });
  }

  listRef.value.toggleRowSelection(row);
};

const select = (selection, row) => {
  if (!props.multiple) {
    selection.forEach(p => {
      if (p.id !== row.id) {
        listRef.value.toggleRowSelection(p);
      }
    });
  }
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
  listRef.value.clearSelection();
};

/**
 * 清空
 */
const clear = () => {
  initState();
};

const getSelectionRows = (): Array<any> => {
  return listRef.value.getSelectionRows();
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  init,
  clear,
  getSelectionRows
});
</script>

<template>
  <div class="filter-container">
    <el-form
      ref="filterRef"
      v-loading="cfg.loading.filter"
      class="filter-form"
      :rules="rules.filter"
      :model="filter"
    >
      <el-row :gutter="cfg.filter.gutter" type="flex">
        <el-col :span="cfg.filter.span">
          <el-form-item>
            <el-input
              v-model="filter.keywords"
              clearable
              :placeholder="t('filter.keywords')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-form-item>
        </el-col>
        <el-col :span="cfg.filter.span">
          <el-form-item>
            <el-select
              v-model="filter.genders"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="2"
              :placeholder="tt('Entity.Employee.EnumGender')"
            >
              <el-option
                v-for="item in cfg.enums.gender"
                :key="item.value"
                :label="item.text"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="cfg.filter.span">
          <el-form-item>
            <el-select
              v-model="filter.status"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
              :max-collapse-tags="3"
              :placeholder="tt('Entity.Employee.EnumStatus')"
            >
              <el-option
                v-for="item in cfg.enums.employeeStatus"
                :key="item.value"
                :label="item.text"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="cfg.filter.span">
          <el-button
            class="query"
            :loading="cfg.loading.list"
            :icon="useRenderIcon('ep:search')"
            @click="query"
          >
            {{ t("operate.query") }}
          </el-button>
        </el-col>
      </el-row>
    </el-form>
  </div>
  <div class="list-container">
    <el-table
      ref="listRef"
      v-loading="cfg.loading.list"
      :data="state.datas"
      :height="props.height"
      :default-sort="cfg.list.defaultSort"
      row-key="id"
      stripe
      border
      class-name="list"
      style="width: 100%"
      @sort-change="sortChange"
      @row-click="rowClick"
      @select="select"
    >
      <template #empty>
        <NoDatas />
      </template>

      <el-table-column
        fixed
        type="selection"
        width="40"
        align="center"
        :label-class-name="props.multiple ? '' : 'header-hidden'"
      />
      <el-table-column
        fixed
        :label="t('list.no')"
        :show-overflow-tooltip="true"
        width="60"
        align="center"
      >
        <template v-slot="scope">
          <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
        </template>
      </el-table-column>
      <el-table-column
        fixed
        sortable="custom"
        prop="loginName"
        :label="tt('Entity.User.LoginName')"
        width="150"
      />
      <el-table-column
        fixed
        sortable="custom"
        prop="displayName"
        :label="tt('Entity.Employee.DisplayName')"
        width="200"
      />
      <el-table-column
        sortable="custom"
        prop="jobNo"
        :label="tt('Entity.Employee.JobNo')"
        width="100"
      />
      <el-table-column
        sortable="custom"
        prop="email"
        :label="tt('Entity.Employee.Email')"
        min-width="200"
      />
      <el-table-column
        sortable="custom"
        prop="Title"
        :label="tt('Entity.Employee.Title')"
        width="150"
      />
      <el-table-column
        sortable="custom"
        prop="position"
        :label="tt('Entity.Employee.Position')"
        width="150"
      />
      <el-table-column
        sortable="custom"
        sort-by="enumGender"
        prop="enumGenderDesc"
        :label="tt('Entity.Employee.EnumGender')"
        width="100"
        align="center"
      />
      <el-table-column
        sortable="custom"
        sort-by="enumStatus"
        prop="enumStatusDesc"
        :label="tt('Entity.Employee.EnumStatus')"
        width="100"
        align="center"
      />
    </el-table>
    <pagination v-model:filter="filter" :total="state.total" @change="getList" />
  </div>
</template>
