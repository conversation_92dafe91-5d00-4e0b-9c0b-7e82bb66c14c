<script setup lang="ts">
import { basicMasterDataApi } from "@/api/basicMasterData";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import { Briefcase } from "@element-plus/icons-vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:station"
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    defaultSort: { prop: "createTime", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0) + (cfg.btn.delete ? 45 : 0))
    }
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  subCompanyId: null,
  departmentId: null,
  stationId: null
});

/**
 * 初始化组件(created时调用)
 */
const initPage = (subCompanyId, departmentId, stationId) => {
  filter.subCompanyId = subCompanyId;
  filter.departmentId = departmentId;
  filter.stationId = stationId;
  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;

  const allInits = [];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据
 */
const getList = () => {
  cfg.loading.list = true;
  basicMasterDataApi
    .QueryStation(filter)
    .then(res => {
      if (res.success) {
        state.datas = res.datas;
        state.total = res.total;
        filter.pageIndex = res.pageIndex;
      }
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 清空
 */
const clear = () => {
  state.datas = [];
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  initPage,
  clear
});
</script>

<template>
  <div>
    <el-row :gutter="10" class="stationInfo">
      <el-col class="station-title" :span="20">
        <el-icon size="15" span="24"><Briefcase /></el-icon>{{ tt("Entity.Station._Entity") }}
      </el-col>
      <el-col class="station-table">
        <el-table
          v-loading="cfg.loading.list"
          :data="state.datas"
          row-key="id"
          stripe
          border
          style="width: 100%; font-size: 12px"
          :default-sort="cfg.list.defaultSort"
          @sort-change="sortChange"
        >
          <template #empty>
            <NoDatas />
          </template>
          <el-table-column
            fixed
            type="index"
            :label="t('list.no')"
            show-overflow-tooltip
            width="60"
            align="center"
          />
          <el-table-column
            fixed
            sortable
            prop="name"
            :label="tt('Entity.Station.Name')"
            width="120"
          />
          <el-table-column
            sortable
            sort-by="department.Name"
            prop="departmentName"
            :label="tt('Entity.Station.Department')"
            width="120"
          />
          <el-table-column
            sortable
            sort-by="position.Name"
            prop="positionName"
            :label="tt('Entity.Station.Position')"
            width="100"
          />
          <el-table-column
            sortable
            sort-by="position.Grade"
            prop="positionGrade"
            :label="tt('Entity.Position.Grade')"
            width="80"
            align="center"
          />
          <el-table-column
            sortable
            prop="startDate"
            :label="tt('Entity.Station.StartDate')"
            width="120"
            align="center"
          />
          <el-table-column
            sortable
            prop="endDate"
            :label="tt('Entity.Station.EndDate')"
            width="120"
            align="center"
          />
          <el-table-column
            sortable
            prop="remark"
            :label="tt('Entity.Station.Remark')"
            min-width="180"
          />
        </el-table>
        <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
      </el-col>
    </el-row>
  </div>
</template>
