<script setup lang="ts">
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { homeApi } from "@/api/home";
import { getColumnOrder, getDefaultOrder } from "@/utils/table";
import View from "./viewcol.vue";
import EpDocument from "@iconify-icons/ep/document";
import Epearch from "@iconify-icons/ep/search";
import Epclose from "@iconify-icons/ep/close-bold";

import { useI18n } from "vue-i18n";
const { t } = useI18n();
const tt = t;

defineOptions({
  name: "announcement:view"
});

/**
 * 基本配置定义
 */
const userStore = useUserStoreHook();
const cfg = reactive({
  filter: {
    gutter: 3,
    span: 6
  },
  list: {
    height: 500,
    defaultSort: { prop: "subject", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0))
    }
  },
  btn: {
    edit: computed(() => {
      return userStore.hasAnyAuth(["Announcement:Manage", "Announcement:Manage:Edit"]);
    })
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  }
});

/**
 * 当前组件ref
 */
const viewRef = ref();
const filterRef = ref();
const listRef = ref();
const listContainerRef = ref();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initFilter();
  query();
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  homeApi
    .QueryAnnouncement(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  keywords: ""
});

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewRef.value?.open(row.id);
};

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 序号
 */
const indexMethod = index => {
  return (filter.pageIndex - 1) * filter.pageSize + index + 1;
};

/**
 * 执行init,created时调用
 */
init();
</script>

<template>
  <div>
    <div class="filter-container">
      <el-form
        ref="filterRef"
        v-loading="cfg.loading.filter"
        class="filter-form"
        :rules="rules.filter"
        :model="filter"
      >
        <el-row :gutter="cfg.filter.gutter" type="flex" justify="start">
          <el-col :span="4">
            <el-input
              v-model="filter.keywords"
              clearable
              :placeholder="tt('Entity.Announcement.Subject')"
              class="filter-item"
              @keyup.enter="query"
            />
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button
              class="query"
              :loading="cfg.loading.list"
              :icon="useRenderIcon(Epearch)"
              @click="query"
            >
              {{ t("operate.query") }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div ref="listContainerRef" class="list-container">
      <el-table
        ref="listRef"
        v-loading="cfg.loading.list"
        :data="state.datas"
        row-key="id"
        stripe
        border
        class-name="list"
        style="width: 100%"
        :height="cfg.list.height"
        :default-sort="cfg.list.defaultSort"
        @sort-change="sortChange"
      >
        <template #empty>
          <NoDatas />
        </template>
        <el-table-column
          fixed
          :label="t('list.no')"
          :index="indexMethod"
          type="index"
          width="70"
          align="center"
        />
        <el-table-column
          fixed
          sortable="custom"
          prop="subject"
          :label="tt('Entity.Announcement.Subject')"
          min-width="250"
        />
        <el-table-column
          sortable="custom"
          prop="startTime"
          :label="tt('Entity.Announcement.StartTime')"
        >
          <template #default="scope">
            <span>{{ scope.row.startTime }}</span>
          </template>
        </el-table-column>

        <el-table-column
          sortable="custom"
          prop="endTime"
          :label="tt('Entity.Announcement.EndTime')"
        >
          <template #default="scope">
            <span>{{ scope.row.endTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          :label="t('list.operate')"
          class-name="operate"
          align="center"
        >
          <template #default="{ row }">
            <el-button
              class="view"
              size="small"
              :circle="true"
              :title="t('operate.view')"
              :icon="useRenderIcon(EpDocument)"
              @click="viewRow(row)"
            />
          </template>
        </el-table-column>
      </el-table>
      <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
    </div>
    <View ref="viewRef" @refresh="getList" />
  </div>
</template>
