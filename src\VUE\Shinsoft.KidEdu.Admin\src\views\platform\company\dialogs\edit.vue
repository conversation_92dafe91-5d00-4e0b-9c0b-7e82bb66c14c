<script setup lang="ts">
import { sysSetupApi } from "@/api/sysSetup";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, FormInstance, FormRules } from "element-plus";
import { EnumFlagsCheckbox } from "@/components/EnumFlags";

defineOptions({
  name: "company:config"
});

const emit = defineEmits(["refresh"]);

const props = defineProps({
  newTitle: {
    type: String,
    default: "新建公司"
  },
  editTitle: {
    type: String,
    default: "编辑公司"
  },
  draggable: {
    type: Boolean,
    default: true
  },
  // 宽度
  width: {
    type: String,
    default: "60%"
  },
  // 字段：间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // 字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // 字段内容：宽度
  formColSpan: {
    type: Number,
    default: 24
  },
  // 按钮：圆角
  btnRound: {
    type: <PERSON>olean,
    default: false
  },
  // 按钮：保存按钮：图标
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // 按钮：关闭按钮：图标
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  }
});

let title = computed(() => {
  return model.value.id ? props.editTitle : props.newTitle;
});

const formRef = ref<FormInstance>();

const visible = ref(false);
const loading = ref(false);
const saveLoading = ref(false);

const defaultModel: Record<string, any> = {
  valid: true
};

const model: Record<string, any> = ref(defaultModel);

const rules: FormRules = {
  code: [
    { required: true, message: "请输入编码", trigger: "blur" },
    { max: 50, message: "编码长度不可以超过50", trigger: "blur" }
  ],
  name: [{ required: true, message: "请输入名称", trigger: "blur" }]
};

const open = (id?: string) => {
  show();
  if (id) {
    get(id);
  }
};

const show = () => {
  visible.value = true;
  model.value = defaultModel;
};

const get = (id: string) => {
  loading.value = true;
  sysSetupApi
    .GetCompany(id)
    .then(res => {
      if (res.success) {
        model.value = res.data;
      } else {
        close();
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      saveLoading.value = true;
      if (model.value.id) {
        update();
      } else {
        add();
      }
    }
  });
};

const add = () => {
  sysSetupApi
    .AddCompany(model.value)
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh("新增成功", "success");
      }
    })
    .finally(() => {
      saveLoading.value = false;
    });
};

const update = () => {
  sysSetupApi
    .UpdateCompany(model.value)
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh("保存成功", "success");
      }
    })
    .finally(() => {
      saveLoading.value = false;
    });
};

const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }

  visible.value = false;
};

const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="loading"
        :rules="rules"
        :model="model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="code" label="编码">
              <el-input v-model="model.code" placeholder="请输入编码" maxlength="50" />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" label="名称">
              <el-input v-model="model.name" placeholder="请输入名称" maxlength="200" />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="shortName" label="简称">
              <el-input v-model="model.shortName" placeholder="请输入名称" maxlength="200" />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="valid" label="有效性">
              <el-switch
                v-model="model.valid"
                class="ml-2"
                inline-prompt
                style="--el-switch-on-color: #13ce66"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            class="save"
            :round="btnRound"
            :disabled="loading"
            :loading="saveLoading"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            保存
          </el-button>
          <el-button
            class="=close"
            :round="btnRound"
            :disabled="loading"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
