<script setup lang="ts">
var dataModel = ref({
  name: "张三",
  mobile: "18100000000",
  address: "上海市徐汇区漕河泾国际服化中心",
  remark:
    "这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试这个是测试"
});

import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;
// var dataModel = defineModel("dataModel");
var formColSpan = 12;
</script>
<template>
  <div>
    <div style="text-align: center; width: 100%">页面page1</div>
    <el-form
      ref="formRef"
      :model="dataModel"
      label-position="right"
      label-width="100"
      class="el-dialog-form"
    >
      <el-row type="flex">
        <el-col :span="formColSpan">
          <el-form-item prop="code" :label="tt('Entity.Role.Code')">
            <!-- <span>{{ dataModel.code }}</span> -->
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="name" :label="tt('Entity.Role.Name')">
            <!-- <span>{{ dataModel.name }}</span> -->
          </el-form-item>
        </el-col>

        <el-col :span="formColSpan">
          <el-form-item prop="enumFlags" :label="tt('Entity.Role.EnumFlags')">
            <!-- <span>{{ dataModel.enumFlagsDesc }}</span> -->
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <el-form-item prop="remark" :label="tt('Entity.Role.Remark')">
            <!-- <span>{{ dataModel.remark }}</span> -->
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
