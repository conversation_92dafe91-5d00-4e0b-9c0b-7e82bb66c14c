<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { ElMessage, ElMessageBox, FormInstance } from "element-plus";
import { getDefaultOrder } from "@/utils/table";
import { announcementApi } from "@/api/announcement";
import Epclose from "@iconify-icons/ep/close-bold";

import { useI18n } from "vue-i18n";
const { t } = useI18n();
const tt = t;

defineOptions({
  name: "announcement:viewcol"
});

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "40%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 24
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model.subject;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      subject: "",
      startTime: "",
      endTime: "",
      publishTime: "",
      content: ""
    }
  },
  // 枚举定义
  enums: {
    roleFlag: []
  },
  list: {
    height: 300,
    defaultSort: { prop: "subject", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0))
    }
  }, // loading：控制变量
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  datas: [],
  model: cfg.default.model as Record<string, any>
});

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件
 */
const init = () => {
  initFilter();
  query();
  get();
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    announcementApi
      .GetAnnouncement(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  announcementApi
    .QueryAnnouncement(filter)
    .then(res => {
      state.datas = res.datas;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  //cfg.loading.filter = true;
};

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  id: ""
});

/**
 * 初始化state数据
 */
const initState = () => {
  state.datas = cfg.default.model;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
};

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      v-model:title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div>
        <el-form
          ref="formRef"
          v-loading="cfg.loading.form"
          :model="state.model"
          label-position="left"
          :label-width="formLabelWidth"
          class="el-dialog-form"
        >
          <!-- <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="publishTime" label="发布日期">
                <span>{{ state.model.publishTime }}</span>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-row :gutter="formGutter">
            <el-col :span="formColSpan">
              <el-form-item prop="content" :label="tt('Entity.Announcement.Content')">
                <span>{{ state.model.content }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <template #footer>
        <div style="text-align: right; padding: 10px 20px">
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(Epclose)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.el-dialog-form {
  padding: 20px;
  font-family: "Arial", sans-serif;
}

.el-dialog {
  border-radius: 10px;
}

.el-dialog__header {
  background-color: #f0f2f5;
  border-bottom: none;
}

.el-dialog__body {
  background-color: #f9fafc;
  padding: 15px;
}

.el-form-item {
  margin-bottom: 15px;
}

.el-form-item label {
  font-weight: bold;
  color: #333;
}

.el-button {
  padding: 10px 15px;
}

.el-row {
  margin-bottom: 10px;
}

.el-message-box {
  font-size: 16px;
}

.el-form-item span {
  font-size: 14px;
  color: #666;
}
</style>
