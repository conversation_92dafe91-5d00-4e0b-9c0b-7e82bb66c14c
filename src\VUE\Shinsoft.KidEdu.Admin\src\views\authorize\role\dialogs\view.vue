<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { allowEdit } from "@/utils/auth";
import { useUserStoreHook } from "@/store/modules/user";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { ElMessage, ElMessageBox } from "element-plus";
import roleInfo from "../components/roleInfo.vue";
import roleAuths from "../components/roleAuths.vue";
import roleMembers from "../components/roleMembers.vue";
import roleEmployees from "../components/roleEmployees.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:view"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.id
    ? `${t("operate.view")} ${tt("Entity.Role._Entity")} - ${state.model.name}`
    : `${t("operate.view")} ${tt("Entity.Role._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumFlags: 0,
      enumEditFlags: 0
    },
    tabsIndex: "roleInfo"
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    tabs: false,
    btn: false
  },
  // 按钮权限
  btn: {
    delete: computed(() => {
      return state.model.id && userStore.hasAnyAuth(["Employee:Manage", "Employee:Manage:Delete"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  model: cfg.default.model as Record<string, any>,
  tabsIndex: cfg.default.tabsIndex,
  tabs: {}
});

/**
 * 当前用户存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const roleInfoRef = ref();
const roleAuthsRef = ref();
const roleMembersRef = ref();
const roleEmployeesRef = ref();

/**
 * 初始化组件（异步）
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予state默认值
  initState();

  get();
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
  state.tabsIndex = cfg.default.tabsIndex;
  state.tabs = {};
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.tabs = true;
    authorizeApi
      .GetRole(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.tabs = false;
      });
  }
};

/**
 * 按钮事件：【删除】
 */
const del = (data?: any) => {
  // 内部调用时，data为空
  // 外部调用时，需要传入{id:""}的对象
  data = data ?? getModel();

  if (data && data.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")} - ${data.name}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "error"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        authorizeApi
          .DeleteRole(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {
        close();
      });
  }
};

const tabChange = name => {
  let inited = state.tabs[name] ?? false;

  if (!inited) {
    state.tabs[name] = true;
  }

  switch (name) {
    case "roleAuths":
      roleAuthsRef.value.init(!inited);
      break;
    case "roleMembers":
      roleMembersRef.value.init(!inited);
      break;
    case "roleEmployees":
      roleEmployeesRef.value.init(!inited);
      break;
  }
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }

  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open,
  del
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      v-model:title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-tabs
        v-model="state.tabsIndex"
        v-loading="cfg.loading.tabs"
        tab-position="left"
        @tab-change="tabChange"
      >
        <el-tab-pane :label="t('shinsoft.basicInfo')" name="roleInfo">
          <role-info ref="roleInfoRef" v-model="state.model" />
        </el-tab-pane>
        <el-tab-pane :label="t('role.tab.auth')" name="roleAuths">
          <role-auths ref="roleAuthsRef" v-model:role-id="state.id" />
        </el-tab-pane>
        <el-tab-pane :label="t('role.tab.member')" name="roleMembers">
          <role-members ref="roleMembersRef" v-model:role-id="state.id" />
        </el-tab-pane>
        <el-tab-pane :label="t('role.tab.employee')" name="roleEmployees">
          <role-employees ref="roleEmployeesRef" v-model:role-id="state.id" />
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.delete && allowEdit(state.model, 1)"
            class="delete"
            style="float: left"
            :round="btnRound"
            :disabled="cfg.loading.tabs"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnDeleteIcon)"
            @click="del()"
          >
            {{ t("operate.delete") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
