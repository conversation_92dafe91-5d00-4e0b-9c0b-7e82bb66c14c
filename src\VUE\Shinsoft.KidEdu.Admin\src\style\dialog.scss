
.el-dialog-content {
  max-height: 60vh;
  overflow-y: auto;
}

.component-content{
  border:1px solid #ccc;
  border-top: 0px;
  border-radius: 6px;
  margin-bottom: 10px;
  box-shadow: 0 2px 6px 0 rgba(0,0,0,.2);
}
.component-content .el-divider{
  width: calc(100% - 4px);
  margin-left: 2px !important;
}


/* 滚动条样式 */
.el-dialog-content::-webkit-scrollbar {
  width: 10px; /*  设置纵轴（y轴）轴滚动条 */
  height: 10px; /*  设置横轴（x轴）轴滚动条 */
}
/* 滚动条滑块（里面小方块） */
.el-dialog-content::-webkit-scrollbar-thumb {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgba(228, 227, 227, 0.6);
}
/* 滚动条轨道 */
.el-dialog-content::-webkit-scrollbar-track {
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  background: rgb(253, 253, 253);
}
/* 按钮 */
.el-dialog-content::-webkit-scrollbar-button {
  border: #ffffff;
  display: block;
  border-style: solid;
  height: 5px;
  width: 5px;
}

/* Up */
::-webkit-scrollbar-button:single-button:vertical:decrement {
  border-width: 0 5px 5px 5px;
  border-color: transparent transparent #c0c0c0 transparent;
}

::-webkit-scrollbar-button:single-button:vertical:decrement:hover {
  border-color: transparent transparent #6d6d6d transparent;
}
/* Down */
::-webkit-scrollbar-button:single-button:vertical:increment {
  border-width: 5px 5px 0 5px;
  border-color: #c0c0c0 transparent transparent transparent;
}

::-webkit-scrollbar-button:vertical:single-button:increment:hover {
  border-color: #6d6d6d transparent transparent transparent;
}

/* left */
::-webkit-scrollbar-button:single-button:horizontal:decrement {
  border-width: 5px 5px 5px 0;
  border-color: transparent transparent #c0c0c0 transparent;
}

::-webkit-scrollbar-button:single-button:horizontal:decrement:hover {
  border-color: transparent transparent #6d6d6d transparent;
}
/* right */
::-webkit-scrollbar-button:single-button:horizontal:increment {
  border-width: 5px 0 5px 5px;
  border-color: #c0c0c0 transparent transparent transparent;
}

::-webkit-scrollbar-button:horizontal:single-button:increment:hover {
  border-color: #6d6d6d transparent transparent transparent;
}