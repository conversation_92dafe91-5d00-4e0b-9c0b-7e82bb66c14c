<script setup lang="ts">
import { ElMessage } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { CostcenterSelector } from "@/components/selector";
import { basicMasterDataApi } from "@/api/basicMasterData";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:deptCostCenter:add"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "75%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return `${t("operate.add")}${tt("Entity.Department._Entity")}${tt("Entity.CostCenter._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // dialog：控制变量
  dialog: {
    visible: false
  },
  form: {
    gutter: 3,
    span: 6,
    labelWidth: "120px"
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return userStore.hasAnyAuth(["Organization:Manage", "Department:Manage:CostCenter"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  models: [],
  companyId: "",
  departmentId: ""
});

/**
 * 存储
 */
const userStore = useUserStoreHook();

/**
 * 当前组件ref
 */
const costcenterSelectorRef = ref();

/**
 * 初始化组件(created时调用)
 */
const init = (subCompanyId?: any) => {
  initFilter().then(() => {
    costcenterSelectorRef.value.init(true, subCompanyId);
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  cfg.loading.form = true;

  const allInits = [];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      resolve();
    });
  });
};

/**
 * 获取model数据
 */
const getModel = () => {
  state.models = [];
  let members = costcenterSelectorRef.value.getSelectionRows();

  if (members?.length > 0) {
    members.forEach(row => {
      let model = {
        companyId: state.companyId,
        departmentId: state.departmentId,
        costCenterId: row.id
      };
      state.models.push(model);
    });
  }

  return state.models;
};

/**
 * 保存
 */
const save = () => {
  const data = getModel();
  if (data.length > 0) {
    cfg.loading.btn = true;
    basicMasterDataApi
      .AddDepartmentCostCenters(data)
      .then(res => {
        // 仅提示
        if (res.success) {
          refresh(t("operate.message.success"), "success");
        }
      })
      .finally(() => {
        cfg.loading.btn = false;
      });
  } else {
    ElMessage({
      message: t("operate.message.noCheckedItems"),
      type: "error",
      duration: 3 * 1000
    });
  }
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (companyId?: any, departmentId?: any) => {
  cfg.dialog.visible = true;
  state.companyId = companyId;
  state.departmentId = departmentId;
  init(companyId);
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-row>
        <el-col :span="24">
          <costcenter-selector ref="costcenterSelectorRef" />
        </el-col>
      </el-row>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.append") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="css" scoped>
:deep(.member-type-button .el-radio-button__inner) {
  width: 120px;
}

:deep(.member-type-button.is-active .el-radio-button__inner) {
  background-color: #2d85dc;
  border-color: #2d85dc;
  box-shadow: -1px 0 0 0 #2d85dc;
}
</style>
