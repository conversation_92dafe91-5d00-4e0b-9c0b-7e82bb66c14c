<script setup lang="ts">
import { ElMessage, ElMessageBox, Sort } from "element-plus";
import { authorizeApi } from "@/api/authorize";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { getColumnOrder, getDefaultOrder } from "@/utils/table";
import { useEnumStoreHook } from "@/store/modules/enum";
import addMember from "./addMember.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:member"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "150px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.roleName
    ? `${t("role.dialog.roleMember")} - ${state.roleName}`
    : `${t("role.dialog.roleMember")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: [],
    total: 0
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  enums: {
    roleMemberType: []
  },
  filter: {
    gutter: 3,
    span: 6
  },
  list: {
    height: 300,
    defaultSort: { prop: "enumType", order: "ascending" } as Sort,
    operate: {
      width: 80
    }
  }, // loading：控制变量
  loading: {
    filter: false,
    list: false,
    btn: false
  },
  // 按钮权限
  btn: {
    member: computed(() => {
      return userStore.hasAnyAuth(["Role:Manage", "Role:Manage:member"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  roleName: "",
  datas: cfg.default.datas,
  total: 0
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  roleId: "",
  pageSize: 5
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const listRef = ref();
const addMemberRef = ref();

/**
 * 初始化组件
 */
const init = () => {
  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;
  const initRoleMemberType = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("RoleMemberType").then(enumInfos => {
        cfg.enums.roleMemberType = enumInfos;
        resolve();
      });
    });
  };

  const allInits = [initRoleMemberType()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 设置model数据
 */
const setModel = (res: QueryResult) => {
  state.datas = res.datas;
  state.total = res.total;
  filter.pageIndex = res.pageIndex;
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
  state.total = cfg.default.total;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  if (filter.roleId) {
    cfg.loading.list = true;
    authorizeApi
      .QueryRoleMember(filter)
      .then(res => {
        setModel(res);
      })
      .finally(() => {
        cfg.loading.list = false;
      });
  }
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

const addRow = () => {
  addMemberRef.value.open(state.id, state.roleName);
};

/**
 * 删除
 */
const deleteRow = (row: any) => {
  ElMessageBox.confirm(
    `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
    `${t("operate.title.deleteConfirm")} - ${row.enumTypeDesc}：${row.memberName}`,
    {
      showClose: false,
      closeOnClickModal: false,
      draggable: true,
      dangerouslyUseHTMLString: true,
      confirmButtonText: t("operate.ok"),
      cancelButtonText: t("operate.cancel"),
      type: "error"
    }
  ).then(() => {
    cfg.loading.list = true;
    authorizeApi.DeleteRoleMember(row).then(res => {
      // 仅提示
      if (res.success) {
        ElMessage({
          message: t("operate.message.success"),
          type: "success",
          duration: 3 * 1000
        });
        getList();
      }
    });
  });
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string, name: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  state.roleName = name;
  filter.roleId = id;

  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div class="filter-container">
        <el-form
          ref="filterRef"
          v-loading="cfg.loading.filter"
          class="filter-form"
          :rules="rules.filter"
          :model="filter"
        >
          <el-row :gutter="cfg.filter.gutter" type="flex">
            <el-col :span="cfg.filter.span">
              <el-form-item>
                <el-select
                  v-model="filter.enumTypes"
                  multiple
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="2"
                  :placeholder="tt('Entity.VwRoleMember.EnumType')"
                >
                  <el-option
                    v-for="item in cfg.enums.roleMemberType"
                    :key="item.value"
                    :label="item.text"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="cfg.filter.span">
              <el-form-item>
                <el-input
                  v-model="filter.memberName"
                  clearable
                  :placeholder="tt('Entity.VwRoleMember.MemberName')"
                  class="filter-item"
                  @keyup.enter="query"
                />
              </el-form-item>
            </el-col>
            <el-col :span="cfg.filter.span">
              <el-button
                class="query"
                :loading="cfg.loading.list"
                :icon="useRenderIcon('ep:search')"
                @click="query"
              >
                {{ t("operate.query") }}
              </el-button>
            </el-col>
          </el-row>
          <el-row :gutter="cfg.filter.gutter" type="flex" justify="end">
            <el-col :span="24" class="buttonbar">
              <el-button
                v-if="cfg.btn.member"
                class="new"
                :icon="useRenderIcon('ep:document-add')"
                @click="addRow"
              >
                {{ t("operate.append") }} {{ tt("Entity.RoleMember._Entity") }}
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="list-container">
        <el-table
          ref="listRef"
          v-loading="cfg.loading.list"
          :data="state.datas"
          row-key="id"
          stripe
          border
          class-name="list"
          style="width: 100%"
          :height="cfg.list.height"
          :default-sort="cfg.list.defaultSort"
          @sort-change="sortChange"
        >
          <template #empty>
            <NoDatas />
          </template>
          <el-table-column
            fixed
            :label="t('list.no')"
            show-overflow-tooltip
            width="60"
            align="center"
          >
            <template v-slot="scope">
              <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            sortable="custom"
            sort-by="enumType"
            prop="enumTypeDesc"
            :label="tt('Entity.VwRoleMember.EnumType')"
            width="120"
          />
          <el-table-column
            sortable="custom"
            prop="memberName"
            :label="tt('Entity.VwRoleMember.MemberName')"
            min-width="200"
          />
          <el-table-column
            v-if="cfg.btn.member"
            fixed="right"
            :label="t('list.operate')"
            class-name="operate"
            :width="cfg.list.operate.width"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                class="delete"
                size="small"
                :circle="true"
                :title="t('operate.delete')"
                :icon="useRenderIcon('ep:delete')"
                @click="deleteRow(row)"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination v-model:filter="filter" :total="state.total" @change="getList" />
      </div>
      <template #footer>
        <div>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <add-member ref="addMemberRef" @refresh="getList" />
  </div>
</template>
