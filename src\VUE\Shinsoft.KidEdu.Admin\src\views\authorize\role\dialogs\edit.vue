<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { ElMessage, FormInstance } from "element-plus";
import { EnumFlagsCheckbox } from "@/components/EnumFlags";
import { allowEdit } from "@/utils/auth";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:edit"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：删除
  btnDeleteIcon: {
    type: String,
    default: "ep:delete"
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  },
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.model?.id
    ? `${t("operate.edit")} ${tt("Entity.Role._Entity")} - ${state.model.name}`
    : `${t("operate.add")} ${tt("Entity.Role._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      enumFlags: 0,
      enumEditFlags: -1
    }
  },
  // 枚举定义
  enums: {
    roleFlag: []
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return state.model.id
        ? allowEdit(state.model, 2 | 4 | 16 | 64) &&
            userStore.hasAnyAuth(["Role:Manage", "Role:Manage:Edit"])
        : userStore.hasAnyAuth(["Role:Manage", "Role:Manage:Add"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  title: "",
  id: "",
  model: cfg.default.model as Record<string, any>
});

/**
 * 验证规则
 */
const rules = {
  form: {
    code: [{ max: 50, message: tt("Rule.Role.Code:Length"), trigger: "blur" }],
    name: [
      { required: true, message: tt("Rule.Role.Name:Required"), trigger: "blur" },
      { max: 50, message: tt("Rule.Role.Name:Length"), trigger: "blur" }
    ],
    remark: [{ max: 500, message: tt("Rule.Role.Remark:Length"), trigger: "blur" }]
  }
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref<FormInstance>();

/**
 * 初始化组件
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时state
  initState();

  cfg.loading.form = true;

  const initRoleFlag = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("RoleFlag", "Edit").then(enumInfos => {
        cfg.enums.roleFlag = enumInfos;
        resolve();
      });
    });
  };

  const allInits = [initRoleFlag()];

  return new Promise<void>(() => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      get();
    });
  });
};

/**
 * 设置model数据
 */
const setModel = (data: any) => {
  state.model = data;
};

/**
 * 获取model数据
 */
const getModel = () => {
  if (!state.model) {
    state.model = cfg.default.model;
  }

  return state.model;
};

/**
 * 初始化state数据
 */
const initState = () => {
  state.model = cfg.default.model;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 获取model数据
 */
const get = () => {
  if (state.id) {
    cfg.loading.form = true;
    authorizeApi
      .GetRole(state.id)
      .then(res => {
        if (res.success) {
          setModel(res.data);
          state.title = state.title + " - " + state.model.name;
        } else {
          close();
        }
      })
      .finally(() => {
        cfg.loading.form = false;
      });
  }
};

/**
 * 新增数据
 */
const add = () => {
  cfg.loading.btn = true;

  const data = getModel();

  authorizeApi
    .AddRole(data)
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 更新数据
 */
const update = () => {
  cfg.loading.btn = true;

  const data = getModel();

  authorizeApi
    .UpdateRole(data)
    .then(res => {
      // 仅提示
      if (res.success) {
        refresh(t("operate.message.success"), "success");
      }
    })
    .finally(() => {
      cfg.loading.btn = false;
    });
};

/**
 * 按钮事件：【保存】
 */
const save = async () => {
  await formRef.value.validate((valid, fields) => {
    if (valid) {
      if (state.model.id) {
        update();
      } else {
        add();
      }
    }
  });
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string) => {
  cfg.dialog.visible = true;
  state.id = id;

  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      :title="title"
      append-to-body
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="formLabelWidth"
        class="el-dialog-form"
      >
        <el-row :gutter="formGutter">
          <el-col :span="formColSpan">
            <el-form-item prop="code" :label="tt('Entity.Role.Code')">
              <el-input
                v-model="state.model.code"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 2)"
                :placeholder="tt('Entity.Role.Code')"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="formColSpan">
            <el-form-item prop="name" :label="tt('Entity.Role.Name')">
              <el-input
                v-model="state.model.name"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 4)"
                :placeholder="tt('Entity.Role.Name')"
                maxlength="100"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="enumFlags" :label="tt('Entity.Role.EnumFlags')">
              <enum-flags-checkbox
                v-model="state.model.enumFlags"
                enumType="RoleFlag"
                :enums="cfg.enums.roleFlag"
                :disabled="!allowEdit(state.model, 16)"
                border
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="formGutter">
          <el-col :span="24">
            <el-form-item prop="remark" :label="tt('Entity.Role.Remark')">
              <el-input
                v-model="state.model.remark"
                clearable
                show-word-limit
                :disabled="!allowEdit(state.model, 64)"
                :placeholder="tt('Entity.Role.Remark')"
                maxlength="500"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.save") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<style lang="css">
.is-not-error > .el-input__wrapper {
  box-shadow: #dcdfe6 !important;
}
</style>
