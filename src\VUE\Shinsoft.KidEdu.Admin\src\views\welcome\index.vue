<script setup lang="ts">
import { goToRoute } from "@/router/utils";
import { t, tt } from "@/plugins/i18n";
import Query from "./announcement/query.vue";
import { useUserStoreHook } from "@/store/modules/user";
const userStore = useUserStoreHook();

defineOptions({
  name: "Welcome"
});
</script>

<template>
  <div>
    <el-row type="flex" :gutter="10" style="padding: 15px">
      <el-col :span="12">
        <Query ref="refQuery" :key="'refQuery' + userStore.currentCompanyId" />
      </el-col>
      <el-col :span="12">
        <Query ref="refQuery" :key="'refQuery1' + userStore.currentCompanyId" />
      </el-col>
    </el-row>
    <!-- 显示 query.vue 的内容 -->
  </div>
</template>
