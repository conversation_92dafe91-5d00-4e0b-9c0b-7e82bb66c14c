import { withInstall } from "@pureadmin/utils";
import employeeSelector from "./employeeSelector.vue";
import stationSelector from "./stationSelector.vue";
import positionSelector from "./positionSelector.vue";
import departmentSelector from "./departmentSelector.vue";
import sysCheckboxSelector from "./sysCheckboxSelector.vue";
import costcenterSelector from "./costcenterSelector.vue";

const EmployeeSelector = withInstall(employeeSelector);
const StationSelector = withInstall(stationSelector);
const PositionSelector = withInstall(positionSelector);
const DepartmentSelector = withInstall(departmentSelector);
const SysCheckboxSelector = withInstall(sysCheckboxSelector);
const CostcenterSelector = withInstall(costcenterSelector);

export {
  EmployeeSelector,
  StationSelector,
  PositionSelector,
  DepartmentSelector,
  SysCheckboxSelector,
  CostcenterSelector
};
