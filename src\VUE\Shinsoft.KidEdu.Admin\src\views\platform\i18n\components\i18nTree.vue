<script setup lang="ts">
import { Plus, EditPen, Delete, FirstAidKit } from "@element-plus/icons-vue";
import { iconUser } from "../utils/static";

defineOptions({
  name: "organization:tree"
});

const emit = defineEmits(["nodeClick", "addI18nCulture", "oprateI18n"]);

const elTreeRef = ref();

/**
 * 点击树节点
 */
const data = defineModel<any>("data");

/**
 * 点击树节点
 */
const handleNodeClick = data => {
  emit("nodeClick", data);
};

/**
 * 添加I18n;
 */
const addI18n = (data: any) => {
  emit("oprateI18n", "add", data);
};

/**
 * 编辑I18n;
 */
const editI18n = (data: any) => {
  emit("oprateI18n", "edit", data);
};

/**
 * 删除I18n;
 */
const deleteI18n = (data: any) => {
  emit("oprateI18n", "delete", data);
};

/**
 * 添加i18nCulture;
 */
const addI18nCulture = (data: any) => {
  emit("addI18nCulture", data);
};

/**
 * 树节点鼠标移入移出;
 */
const mouseover = data => {
  data.show = true;
};

/**
 * 树节点鼠标移入;
 */
const mouseleave = data => {
  data.show = false;
};

/**
 * 关键字;
 */
const filterText = ref<string>("");

/**
 * 监听关键字;
 */
watch(filterText, val => {
  elTreeRef.value!.filter(val);
});

/**
 * 关键字查询;
 */
const filterNode = (value: string, data: any) => {
  if (!value) return true;
  return data.name.includes(value);
};
</script>

<template>
  <div>
    <el-input v-model="filterText" placeholder="关键字" />
    <div class="rootBtn">
      <a @click="addI18n(data)">
        <el-icon><FirstAidKit /></el-icon>添加根节点</a
      >
    </div>
    <div class="scrollable-tree">
      <el-tree
        ref="elTreeRef"
        :data="data"
        node-key="level"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
      >
        <template #default="{ node, data }">
          <div class="treeNode" @mouseover="mouseover(data)" @mouseleave="mouseleave(data)">
            <div class="treeOprate_Left" @click="handleNodeClick(data)">
              <span class="custom-tree-node">
                <span
                  :class="[node.childNodes.length ? 'bold' : '', node.isCurrent ? 'orange' : '']"
                >
                  <a> {{ data.key }} </a>
                </span>
              </span>
            </div>
            <div v-if="data.show" class="treeOprate_Right">
              <span>
                <a :style="{ marginRight: '0.5rem' }" title="添加I18n数据" @click="addI18n(data)">
                  <el-icon size="15" span="24"><Plus /></el-icon>
                </a>
                <a :style="{ marginRight: '0.5rem' }" title="编辑I18n数据" @click="editI18n(data)">
                  <el-icon size="15" span="24"><EditPen /></el-icon>
                </a>
                <a
                  :style="{ marginRight: '0.5rem' }"
                  title="删除I18n数据"
                  @click="deleteI18n(data)"
                >
                  <el-icon size="15" span="24"><Delete /></el-icon>
                </a>
                <a
                  :style="{ marginRight: '0.5rem' }"
                  title="添加I18nCulture数据"
                  @click="addI18nCulture(data)"
                >
                  <el-icon size="15" span="24"><iconUser /></el-icon>
                </a>
              </span>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>
<style lang="scss" scoped>
.icon {
  width: 18px;
  height: 18px;
  margin-right: 3px;
}
.treeOprate_Left {
  width: 60%;
  float: left;
}
.treeOprate_Right {
  width: 39%;
  text-align: right;
  float: right;
}
.treeNode {
  width: 100%;
}
.scrollable-tree {
  height: 90vh;
  float: left;
  width: 100%;
  overflow-y: auto; /* 启用自动滚动条 */
}
.rootBtn {
  float: left;
  width: 100%;
  margin: 3px 5px 3px 8px;
  font-size: 14px;
}
</style>
