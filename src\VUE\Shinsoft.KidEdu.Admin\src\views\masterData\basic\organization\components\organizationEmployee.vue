<script setup lang="ts">
import { Avatar } from "@element-plus/icons-vue";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { basicMasterDataApi } from "@/api/basicMasterData";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import { ElMessage, ElMessageBox } from "element-plus";
import AddStationEmployee from "../dialogs/addStationEmployee.vue";
import EditEmployeeStation from "../dialogs/editEmployeeStation.vue";
import ViewEmployee from "@/views/authorize/employee/dialogs/view.vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "organization:employee"
});

/**
 * 当前组件ref
 */
const addEmployeeStationRef = ref();
const editEmployeeStationRef = ref();
const viewEmployeeRef = ref();

/**
 * 基本配置定义
 */
const cfg = reactive({
  filter: {
    gutter: 3,
    span: 4
  },
  list: {
    defaultSort: { prop: "createTime", order: "ascending" },
    operate: {
      width: computed(() => 40 + (cfg.btn.edit ? 45 : 0) + (cfg.btn.delete ? 45 : 0))
    }
  },
  // loading：控制变量
  loading: {
    filter: false,
    list: false
  },
  showOperate: false
});

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：总数
  total: 0,
  // 数据列表：当前页
  datas: []
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  subCompanyId: null,
  departmentId: null,
  stationId: null
});

/**
 * 初始化组件(created时调用)
 */
const initPage = (subCompanyId, departmentId, stationId) => {
  filter.subCompanyId = subCompanyId;
  filter.departmentId = departmentId;
  filter.stationId = stationId;
  if (stationId) {
    cfg.showOperate = true;
  } else {
    cfg.showOperate = false;
  }

  initFilter().then(() => {
    query();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  filter.order = getDefaultOrder(cfg.list.defaultSort);

  cfg.loading.filter = true;

  const allInits = [];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.filter = false;
      resolve();
    });
  });
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  basicMasterDataApi
    .QueryEmployeeStation(filter)
    .then(res => {
      state.datas = res.datas;
      state.total = res.total;
      filter.pageIndex = res.pageIndex;
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

/**
 * 清空
 */
const clear = () => {
  state.datas = [];
};

/**
 * 人员在岗：添加人员
 */
const addEmployees = () => {
  addEmployeeStationRef.value.open(filter.subCompanyId, filter.stationId);
};

/**
 * 按钮事件：【删除】
 */
const del = (data?: any) => {
  if (data && data.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")} - ${data.stationName}:${data.employeeName}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "warning"
      }
    )
      .then(() => {
        cfg.loading.btn = true;
        basicMasterDataApi
          .DeleteEmployeeStation(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              query();
              ElMessage({
                message: t("operate.message.success"),
                type: "success",
                duration: 3 * 1000
              });
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      })
      .catch(() => {});
  }
};

/**
 * 查看：点击列表中【查看】按钮事件
 */
const viewRow = (row: any) => {
  viewEmployeeRef.value?.open(row.employeeId);
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editEmployeeStationRef.value?.open(row.id);
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  initPage,
  clear
});
</script>

<template>
  <div>
    <el-row :gutter="10" class="stationInfo">
      <el-col class="station-title" :span="20">
        <el-icon size="15" span="24"><Avatar /></el-icon>{{ tt("Entity.EmployeeStation.Employee") }}
      </el-col>
      <el-col class="buttonbar" :span="4">
        <el-button
          v-if="cfg.showOperate"
          class="new"
          :icon="useRenderIcon('ep:document-add')"
          size="small"
          @click="addEmployees"
        >
          {{ t("operate.append") }}{{ tt("Entity.EmployeeStation.Employee") }}
        </el-button>
      </el-col>
      <el-col>
        <el-row :gutter="cfg.filter.gutter" type="flex">
          <el-col :span="cfg.filter.span">
            <el-form-item>
              <el-input
                v-model="filter.keywords"
                clearable
                :placeholder="t('filter.keywords')"
                class="filter-item"
                @keyup.enter="query"
              />
            </el-form-item>
          </el-col>
          <el-col :span="cfg.filter.span">
            <el-button class="query" :loading="cfg.loading.list" @click="query">{{
              t("operate.query")
            }}</el-button>
          </el-col>
        </el-row>
      </el-col>
      <el-col class="station-table">
        <el-table
          v-loading="cfg.loading.list"
          :data="state.datas"
          row-key="id"
          stripe
          border
          style="width: 100%; font-size: 12px"
          :default-sort="cfg.list.defaultSort"
          @sort-change="sortChange"
        >
          <template #empty>
            <NoDatas />
          </template>
          <el-table-column
            fixed
            :label="t('list.no')"
            :show-overflow-tooltip="true"
            width="60"
            align="center"
          >
            <template v-slot="scope">
              <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column
            fixed
            sortable="custom"
            sort-by="employee.DisplayName"
            prop="employeeName"
            :label="tt('Entity.Employee.DisplayName')"
            min-width="100"
          />
          <el-table-column
            sortable="custom"
            sort-by="employee.JobNo"
            prop="employeeJobNo"
            :label="tt('Entity.Employee.JobNo')"
            min-width="100"
          />
          <el-table-column
            sortable="custom"
            sort-by="station.Name"
            prop="stationName"
            :label="tt('Entity.Employee.EmployeeStation')"
            min-width="100"
          />
          <el-table-column
            sortable="custom"
            sort-by="Employee.Position"
            prop="positionName"
            :label="tt('Entity.Employee.Position')"
            min-width="100"
          />
          <el-table-column
            sortable="custom"
            prop="startDate"
            :label="tt('Entity.EmployeeStation.StartDate')"
            min-width="100"
          />
          <el-table-column
            sortable="custom"
            prop="endDate"
            :label="tt('Entity.EmployeeStation.EndDate')"
            min-width="100"
          />
          <el-table-column prop="onDuty" :label="t('list.onDuty')" width="100" align="center" />
          <el-table-column
            fixed="right"
            :label="t('list.operate')"
            class-name="operate"
            :width="150"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                class="view"
                size="small"
                :circle="true"
                :title="t('operate.view')"
                :icon="useRenderIcon('ep:document')"
                @click="viewRow(row)"
              />
              <el-button
                class="edit"
                size="small"
                :circle="true"
                :title="t('operate.edit')"
                :icon="useRenderIcon('ep:edit')"
                @click="editRow(row)"
              />
              <el-button
                class="delete"
                size="small"
                :circle="true"
                :title="t('operate.delete')"
                :icon="useRenderIcon('ep:delete')"
                @click="del(row)"
              />
            </template>
          </el-table-column>
        </el-table>
        <Pagination v-model:filter="filter" :total="state.total" @change="getList" />
      </el-col>
    </el-row>
    <AddStationEmployee ref="addEmployeeStationRef" @refresh="getList" />
    <EditEmployeeStation ref="editEmployeeStationRef" @refresh="getList" />
    <ViewEmployee ref="viewEmployeeRef" @refresh="getList" />
  </div>
</template>
