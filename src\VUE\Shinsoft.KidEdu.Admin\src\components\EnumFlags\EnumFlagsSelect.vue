<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "EnumFlagsSelect"
});

const emit = defineEmits(["change"]);

const model = defineModel("modelValue", {
  type: Number,
  default: 0,
  get: val => {
    let enumValue = 0;

    props.enums.forEach(enumInfo => {
      enumValue |= enumInfo.value;
    });

    if (state.orgValue !== val || state.orgEnumsValue !== enumValue) {
      state.orgValue = val;
      state.orgEnumsValue = enumValue;
      setValues(val);
    }

    return val;
  }
});

const state = reactive({
  values: [],
  orgValue: 0,
  extValue: 0,
  orgEnumsValue: 0
});

const setValues = (value: number) => {
  const newValues = [];
  state.extValue = value;

  props.enums.forEach(enumInfo => {
    if ((enumInfo.value & value) !== 0) {
      newValues.push(enumInfo.value);
      state.extValue &= ~enumInfo.value;
    }
  });

  state.values = newValues;
};

const setModel = (values: Array<number>) => {
  let newValue = state.extValue;
  values.forEach((flag: number) => {
    newValue |= flag;
  });
  model.value = newValue;
};

const props = defineProps({
  enumType: {
    required: true,
    type: String
  },
  enums: {
    required: true,
    type: Array<any>
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否可以清空选项
  clearable: {
    type: Boolean,
    default: false
  },
  // 多选时是否将选中值按文字的形式展示
  collapseTags: {
    type: Boolean,
    default: false
  },
  // 当鼠标悬停于折叠标签的文本时，是否显示所有选中的标签。 要使用此属性，collapse-tags属性必须设定为 true
  collapseTagsTooltip: {
    type: Boolean,
    default: false
  },
  // multiple 属性设置为 true 时，代表多选场景下用户最多可以选择的项目数， 为 0 则不限制
  multipleLimit: {
    type: Number,
    default: 0
  },
  //Select 输入框的原生 name 属性
  name: {
    type: String
  },
  // Select 输入框的原生 autocomplete 属性
  autocomplete: {
    type: String
  },
  // 占位符，默认为“Select”
  placeholder: {
    type: String
  },
  maxCollapseTags: {
    type: Number,
    default: 1
  },
  //是否禁用选项
  allowDisableOption: {
    type: Boolean,
    default: false
  }
});

const change = newValues => {
  setModel(newValues);
  emit("change", newValues);
};
</script>

<template>
  <el-select
    ref="selectRef"
    v-model="state.values"
    :disabled="props.disabled"
    :multiple="true"
    :clearable="true"
    :collapse-tags="props.collapseTags"
    :collapse-tags-tooltip="props.collapseTagsTooltip"
    :multiple-limit="props.multipleLimit"
    :max-collapse-tags="props.maxCollapseTags"
    :placeholder="props.placeholder"
    :autocomplete="props.autocomplete"
    :name="props.name"
    @change="change"
  >
    <el-option
      v-for="item in props.enums"
      :key="item.value"
      :label="props.enumType ? `${tt('Enums.' + enumType + '.' + item.name)}` : item.text"
      :value="item.value"
      :disabled="props.allowDisableOption && item.disabled"
    />
  </el-select>
  <el-input-number v-show="false" v-model="model" />
</template>
