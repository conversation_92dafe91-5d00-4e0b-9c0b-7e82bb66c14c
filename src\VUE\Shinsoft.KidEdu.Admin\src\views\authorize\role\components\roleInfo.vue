<script setup lang="ts">
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:view:info"
});

/**
 * 定义属性
 */
const props = defineProps({
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "80px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 12
  }
});

const model = defineModel<Record<string, any>>("modelValue", {
  required: true,
  default: {}
});
</script>

<template>
  <div>
    <el-form
      ref="formRef"
      :model="model"
      label-position="right"
      :label-width="formLabelWidth"
      class="el-dialog-form"
    >
      <el-row :gutter="formGutter">
        <el-col :span="formColSpan">
          <el-form-item prop="code" :label="tt('Entity.Role.Code')">
            <span>{{ model.code }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="formColSpan">
          <el-form-item prop="name" :label="tt('Entity.Role.Name')">
            <span>{{ model.name }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="formGutter">
        <el-col :span="formColSpan">
          <el-form-item prop="enumFlags" :label="tt('Entity.Role.EnumFlags')">
            <span>{{ model.enumFlagsDesc }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="formGutter">
        <el-col :span="24">
          <el-form-item prop="remark" :label="tt('Entity.Role.Remark')">
            <span>{{ model.remark }}</span>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>
