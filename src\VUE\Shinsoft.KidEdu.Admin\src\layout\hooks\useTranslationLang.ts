import { useNav } from "./useNav";
import { useI18n } from "vue-i18n";
import { useRoute } from "vue-router";
import { watch, onBeforeMount, type Ref } from "vue";
import { useUserStore } from "@/store/modules/user";
import { useEnumStore } from "@/store/modules/enum";

export function useTranslationLang(ref?: Ref) {
  const { $storage, changeTitle, handleResize } = useNav();
  const { locale, t } = useI18n();
  const route = useRoute();

  function translationCulture(culture: string) {
    useUserStore()
      .switchCulture(culture)
      .then(() => {
        // setCulture(culture);
        $storage.locale = { locale: culture };
        locale.value = culture;
        useEnumStore().clear();
        ref && handleResize(ref.value);
      });
  }

  function translationCh() {
    $storage.locale = { locale: "zh-CN" };
    locale.value = "zh-CN";
    ref && handleResize(ref.value);
  }

  function translationEn() {
    $storage.locale = { locale: "en" };
    locale.value = "en";
    ref && handleResize(ref.value);
  }

  watch(
    () => locale.value,
    () => {
      changeTitle(route.meta);
    }
  );

  onBeforeMount(() => {
    locale.value = $storage.locale?.locale ?? "zh-CN";
  });

  return {
    t,
    route,
    locale,
    translationCulture,
    translationCh,
    translationEn
  };
}
