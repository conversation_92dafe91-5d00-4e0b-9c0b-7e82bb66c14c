<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { Sort } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useI18n } from "vue-i18n";
import { ElMessage, FormInstance } from "element-plus";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "employeeStations"
});

const employeeId = defineModel<string>("employeeId");

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: []
  },
  // loading：控制变量
  loading: {
    list: false
  },
  // 列表相关配置
  list: {
    // 高度
    height: 250,
    // 默认排序
    defaultSort: reactive<Sort>({ prop: "positionGrade", order: "descending" })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  datas: cfg.default.datas
});

/**
 * 初始化组件
 */
const init = () => {
  // 防止之前打开的数据残留，因此初始化时赋予state默认值
  initState();
  getList();
};

/**
 * 获取列表数据
 */
const getList = () => {
  if (employeeId.value) {
    cfg.loading.list = true;
    authorizeApi
      .GetEmployeeStations(employeeId.value)
      .then(res => {
        if (res.success) {
          state.datas = res.data;
        }
      })
      .finally(() => {
        cfg.loading.list = false;
      });
  }
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
};

/**
 * 清空
 */
const clear = () => {
  initState();
};

/**
 * 设为主岗
 */
const setMajorStation = data => {
  const employeeModel = {
    id: employeeId.value,
    majorStationId: data.id
  };
  console.log(employeeModel);
  authorizeApi
    .SetEmployeeMajor(employeeModel)
    .then(res => {
      if (res.success) {
        ElMessage({
          message: t("operate.message.success"),
          type: "success",
          duration: 3 * 1000
        });
        getList();
      }
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  init,
  clear
});
</script>

<template>
  <div class="list-container">
    <el-table
      v-loading="cfg.loading.list"
      :data="state.datas"
      :height="cfg.list.height"
      :default-sort="cfg.list.defaultSort"
      row-key="id"
      stripe
      border
      class-name="list"
      style="width: 100%"
    >
      <template #empty>
        <NoDatas />
      </template>
      <el-table-column fixed type="index" label="序号" show-overflow-tooltip width="60" />
      <el-table-column fixed sortable prop="name" label="岗位名" width="120" />
      <el-table-column sortable prop="departmentName" label="部门" width="120" />
      <el-table-column sortable prop="positionName" label="职位" width="100" />
      <el-table-column sortable prop="positionGrade" label="职级" width="80" align="center" />
      <el-table-column sortable prop="startDate" label="到岗日期" width="120" align="center" />
      <el-table-column sortable prop="endDate" label="离岗日期" width="120" align="center" />
      <el-table-column sortable prop="isMajorStation" label="是否主岗" width="120" align="center" />
      <el-table-column sortable prop="remark" label="备注" min-width="200" />
      <el-table-column fixed="right" label="操作" class-name="operate" :width="80" align="center">
        <template #default="{ row }">
          <el-button
            :disabled="row.isMajorStation !== '否'"
            class="view"
            size="small"
            :circle="true"
            :title="t('operate.setMajorStation')"
            :icon="useRenderIcon('ep:setting')"
            @click="setMajorStation(row)"
          />
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
