<script setup lang="ts">
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { personApi } from "@/api/person";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import { getDefaultOrder, getColumnOrder } from "@/utils/table";
import { ElMessage, ElMessageBox, FormInstance, Sort } from "element-plus";
import { EnumFlagsCheckbox } from "@/components/EnumFlags";
import EditDelegate from "./editDelegate.vue";

defineOptions({
  name: "account:myDelegate"
});

/**
 * 定义属性
 */
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: "设置代理"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "60%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  }
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    datas: [],
    total: 0
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  filter: {
    gutter: 3,
    span: 6
  },
  list: {
    height: 300,
    defaultSort: { prop: "valid", order: "descending" } as Sort,
    operate: {
      width: 80
    }
  }, // loading：控制变量
  loading: {
    filter: false,
    list: false,
    btn: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  datas: cfg.default.datas,
  total: 0
});

/**
 * 查询过滤条件
 */
const filter: PagingFilter = reactive({
  agentKeywords: "",
  valids: [],
  dates: [],
  pageSize: 5
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  filter: {}
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const listRef = ref();
const editRef = ref();

/**
 * 初始化组件
 */
const init = () => {
  query();
};

/**
 * 设置model数据
 */
const setModel = (res: QueryResult) => {
  state.datas = res.datas;
  state.total = res.total;
  filter.pageIndex = res.pageIndex;
};

/**
 * 初始化state
 */
const initState = () => {
  state.datas = cfg.default.datas;
  state.total = cfg.default.total;
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
};

/**
 * 获取列表数据（异步）
 */
const getList = () => {
  cfg.loading.list = true;
  personApi
    .QueryMyDelegates(filter)
    .then(res => {
      setModel(res);
    })
    .finally(() => {
      cfg.loading.list = false;
    });
};

/**
 * 查询：点击【查询】按钮事件
 */
const query = () => {
  filter.pageIndex = 1;
  getList();
};

/**
 * 排序：点击表头中【字段】排序事件
 */
const sortChange = (column, prop, order) => {
  filter.pageIndex = 1;
  filter.order = getColumnOrder(column);
  getList();
};

const addRow = () => {
  editRef.value.open();
};

/**
 * 编辑：点击列表中【编辑】按钮事件
 */
const editRow = (row: any) => {
  editRef.value?.open(row.id);
};

/**
 * 删除
 */
const deleteRow = (row: any) => {
  // ElMessageBox.confirm(
  //   `请确认是否要删除角色成员【${row.enumTypeDesc}：${row.memberName}】?<br />提示：删除后将不可恢复!`,
  //   "删除确认",
  //   {
  //     showClose: false,
  //     closeOnClickModal: false,
  //     draggable: true,
  //     dangerouslyUseHTMLString: true,
  //     confirmButtonText: "确认",
  //     cancelButtonText: "取消",
  //     type: "error"
  //   }
  // ).then(() => {
  //   cfg.loading.list = true;
  //   authorizeApi.DeleteRoleMember(row).then(res => {
  //     // 仅提示
  //     if (res.success) {
  //       ElMessage({
  //         message: "删除成功",
  //         type: "success",
  //         duration: 3 * 1000
  //       });
  //       getList();
  //     }
  //   });
  // });
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = () => {
  cfg.dialog.visible = true;

  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <div class="filter-container">
        <el-form
          ref="filterRef"
          v-loading="cfg.loading.filter"
          class="filter-form"
          :rules="rules.filter"
          :model="filter"
        >
          <el-row :gutter="cfg.filter.gutter" type="flex">
            <el-col :span="cfg.filter.span">
              <el-form-item>
                <el-input
                  v-model="filter.agentKeywords"
                  clearable
                  placeholder="代理人"
                  class="filter-item"
                  @keyup.enter="query"
                />
              </el-form-item>
            </el-col>
            <el-col :span="cfg.filter.span">
              <el-form-item>
                <el-select
                  v-model="filter.valids"
                  multiple
                  clearable
                  collapse-tags
                  collapse-tags-tooltip
                  :max-collapse-tags="2"
                  placeholder="有效性"
                >
                  <el-option label="有效" :value="true" />
                  <el-option label="无效" :value="false" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="cfg.filter.span">
              <el-form-item>
                <el-date-picker
                  v-model="filter.dates"
                  type="daterange"
                  unlink-panels
                  range-separator="至"
                  start-placeholder="开始"
                  end-placeholder="结束"
                />
              </el-form-item>
            </el-col>
            <el-col :span="cfg.filter.span">
              <el-button
                class="query"
                :loading="cfg.loading.list"
                :icon="useRenderIcon('ep:search')"
                @click="query"
              >
                查询
              </el-button>
              <el-button
                v-if="!userStore.isAgent"
                class="new"
                :icon="useRenderIcon('ep:document-add')"
                @click="addRow"
              >
                添加代理
              </el-button>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="list-container">
        <el-table
          ref="listRef"
          v-loading="cfg.loading.list"
          :data="state.datas"
          row-key="id"
          stripe
          border
          class-name="list"
          style="width: 100%"
          :height="cfg.list.height"
          :default-sort="cfg.list.defaultSort"
          @sort-change="sortChange"
        >
          <template #empty>
            <NoDatas />
          </template>
          <el-table-column fixed label="序号" show-overflow-tooltip width="60" align="center">
            <template v-slot="scope">
              <span>{{ (filter.pageIndex - 1) * filter.pageSize + scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column sortable="custom" prop="agentName" label="代理人" min-width="200" />
          <el-table-column
            sortable="custom"
            prop="startDate"
            label="开始"
            width="150"
            align="center"
          />
          <el-table-column
            sortable="custom"
            prop="endDate"
            label="结束"
            width="150"
            align="center"
          />
          <el-table-column
            sortable="custom"
            prop="valid"
            label="有效性"
            width="100"
            align="center"
            class-name="icon"
          >
            <template #default="{ row }">
              <el-icon v-if="row.valid" title="有效">
                <icon-ep-circle-check />
              </el-icon>
              <el-icon v-else title="无效">
                <icon-ep-circle-close />
              </el-icon>
            </template>
          </el-table-column>
          <el-table-column
            v-if="!userStore.isAgent"
            fixed="right"
            label="操作"
            class-name="operate"
            :width="100"
            align="center"
          >
            <template #default="{ row }">
              <el-button
                class="edit"
                size="small"
                :circle="true"
                title="编辑"
                :icon="useRenderIcon('ep:edit')"
                @click="editRow(row)"
              />
              <el-button
                class="delete"
                size="small"
                :circle="true"
                title="删除"
                :icon="useRenderIcon('ep:delete')"
                @click="deleteRow(row)"
              />
            </template>
          </el-table-column>
        </el-table>
        <pagination v-model:filter="filter" :total="state.total" @change="getList" />
      </div>
      <template #footer>
        <div>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>

    <edit-delegate ref="editRef" />
  </div>
</template>
