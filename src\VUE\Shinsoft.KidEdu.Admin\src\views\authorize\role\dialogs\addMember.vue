<script setup lang="ts">
import { ElMessage } from "element-plus";
import { useRenderIcon } from "@/components/ReIcon/src/hooks";
import { useUserStoreHook } from "@/store/modules/user";
import { useEnumStoreHook } from "@/store/modules/enum";
import {
  EmployeeSelector,
  StationSelector,
  PositionSelector,
  DepartmentSelector
} from "@/components/selector";
import { authorizeApi } from "@/api/authorize";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:member:add"
});

/**
 * 定义回调事件
 * refresh：刷新回调事件
 */
const emit = defineEmits(["refresh"]);

/**
 * 定义属性
 */
const props = defineProps({
  // 标题
  addTitle: {
    type: String,
    default: "添加角色成员"
  },
  // dialog：是否可拖拽
  draggable: {
    type: Boolean,
    default: true
  },
  // dialog：宽度
  width: {
    type: String,
    default: "75%"
  },
  // dialog：按钮：是否圆角
  btnRound: {
    type: Boolean,
    default: false
  },
  // dialog：按钮图标：保存
  btnSaveIcon: {
    type: String,
    default: "ep:select"
  },
  // dialog：按钮图标：关闭
  btnCloseIcon: {
    type: String,
    default: "ep:close-bold"
  }
});

/**
 * dialog：标题
 */
const title = computed(() => {
  return state.roleName
    ? `${t("operate.add")}${tt("Entity.RoleMember._Entity")}- ${state.roleName}`
    : `${t("operate.add")}${tt("Entity.RoleMember._Entity")}`;
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    model: {
      roleId: "",
      enumMemberType: 0,
      memberIds: []
    }
  },
  // dialog：控制变量
  dialog: {
    visible: false
  },
  enums: {
    roleMemberType: []
  },
  form: {
    gutter: 3,
    span: 6,
    labelWidth: "120px"
  },
  // loading：控制变量
  loading: {
    form: false,
    btn: false
  },
  // 按钮权限
  btn: {
    save: computed(() => {
      return userStore.hasAnyAuth(["Role:Manage", "Role:Manage:member"]);
    })
  }
});

/**
 * 数据变量
 */
const state = reactive({
  id: "",
  roleName: "",
  model: {
    roleId: "",
    memberType: 0,
    memberIds: []
  },
  // 选择器初始化变量
  roleMemberType: {}
});

/**
 * 验证规则
 */
const rules = {
  // 查询条件验证规则，一般情况下不需要设置
  form: {
    roleId: [{ required: true, message: tt("Rule.RoleMember.RoleId:Required"), trigger: "blur" }],
    memberType: [
      { required: true, message: tt("Rule.RoleMember.MemberType:Required"), trigger: "blur" }
    ]
  }
};

/**
 * 存储
 */
const userStore = useUserStoreHook();
const enumStore = useEnumStoreHook();

/**
 * 当前组件ref
 */
const formRef = ref();
const employeeSelectorRef = ref();
const stationSelectorRef = ref();
const positionSelectorRef = ref();
const departmentSelectorRef = ref();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  initState();
  initFilter().then(() => {
    changeMemberType();
  });
};

/**
 * 初始化查询条件（异步）
 */
const initFilter = () => {
  cfg.loading.form = true;
  const initRoleMemberType = async () => {
    return new Promise<void>(resolve => {
      enumStore.getEnumInfos("RoleMemberType").then(enumInfos => {
        cfg.enums.roleMemberType = enumInfos;
        enumInfos.forEach(enumInfo => {
          state.roleMemberType[enumInfo.value] = false;
        });

        if (enumInfos.length > 0) {
          state.model.memberType = enumInfos[0].value;
        }

        resolve();
      });
    });
  };

  const allInits = [initRoleMemberType()];

  return new Promise<void>(resolve => {
    Promise.all(allInits).then(() => {
      cfg.loading.form = false;
      resolve();
    });
  });
};

/**
 * 获取model数据
 */
const getModel = () => {
  state.model.memberIds = [];

  let members: Array<any> = null;

  switch (state.model.memberType) {
    case 2:
      members = employeeSelectorRef.value.getSelectionRows();
      break;
    case 3:
      members = stationSelectorRef.value.getSelectionRows();
      break;
    case 4:
      members = positionSelectorRef.value.getSelectionRows();
      break;
    case 5:
      members = departmentSelectorRef.value.getSelectionRows();
      break;
  }

  if (members?.length > 0) {
    members.forEach(row => {
      state.model.memberIds.push(row.id);
    });
  }

  return state.model;
};

/**
 * 初始化state
 */
const initState = () => {
  state.roleMemberType = {};
};

/**
 * 清除state数据
 */
const clearState = () => {
  initState();
  formRef.value.clearValidate();
  formRef.value.resetFields();
};

/**
 * 保存
 */
const save = async () => {
  const data = getModel();

  await formRef.value.validate((valid, fields) => {
    if (valid) {
      if (data.memberIds.length > 0) {
        cfg.loading.btn = true;
        authorizeApi
          .AddRoleMembers(data)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {
            cfg.loading.btn = false;
          });
      } else {
        ElMessage({
          message: tt("Rule.RoleMember.Member:Required"),
          type: "error",
          duration: 3 * 1000
        });
      }
    }
  });
};

/**
 * 改变成员类型
 */
const changeMemberType = (value?: any) => {
  let inited = state.roleMemberType[state.model.memberType] ?? false;

  if (!inited) {
    state.roleMemberType[state.model.memberType] = true;
  }

  switch (state.model.memberType) {
    case 2:
      employeeSelectorRef.value.init(!inited);
      break;
    case 3:
      stationSelectorRef.value.init(!inited);
      break;
    case 4:
      positionSelectorRef.value.init(!inited);
      break;
    case 5:
      departmentSelectorRef.value.init(!inited);
      break;
  }
};

// 以下方法一般不需要修改

/**
 * dialog:显示(父组件调用)
 */
const open = (id: string, name: string) => {
  cfg.dialog.visible = true;
  state.id = id;
  state.roleName = name;
  state.model.roleId = id;

  init();
};

/**
 * dialog：关闭事件
 */
const close = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  clearState();
  if (msg) {
    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
  cfg.dialog.visible = false;
};

/**
 * 刷新回调事件
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  close(msg, type);
  emit("refresh");
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  open
});
</script>

<template>
  <div>
    <el-dialog
      v-model="cfg.dialog.visible"
      append-to-body
      :title="title"
      :draggable="draggable"
      :width="width"
      :close-on-click-modal="false"
      @close="close"
    >
      <el-form
        ref="formRef"
        v-loading="cfg.loading.form"
        :rules="rules.form"
        :model="state.model"
        label-position="right"
        :label-width="cfg.form.labelWidth"
      >
        <el-row :gutter="cfg.form.gutter" type="flex">
          <el-col :span="24">
            <el-form-item prop="enumMemberType" :label="tt('Entity.RoleMember.EnumType')">
              <el-radio-group v-model="state.model.memberType" @change="changeMemberType">
                <el-radio-button
                  v-for="item in cfg.enums.roleMemberType"
                  :key="item.value"
                  :label="item.text"
                  :value="item.value"
                  class="member-type-button"
                />
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="cfg.form.span">
            <el-form-item prop="memberIds" />
          </el-col>
        </el-row>
      </el-form>
      <el-row v-show="state.model.memberType === 2">
        <el-col :span="24">
          <employee-selector ref="employeeSelectorRef" />
        </el-col>
      </el-row>
      <el-row v-show="state.model.memberType === 3">
        <el-col :span="24">
          <station-selector ref="stationSelectorRef" />
        </el-col>
      </el-row>
      <el-row v-show="state.model.memberType === 4">
        <el-col :span="24">
          <position-selector ref="positionSelectorRef" />
        </el-col>
      </el-row>
      <el-row v-show="state.model.memberType === 5">
        <el-col :span="24">
          <department-selector ref="departmentSelectorRef" />
        </el-col>
      </el-row>
      <template #footer>
        <div>
          <el-button
            v-if="cfg.btn.save"
            class="save"
            :round="btnRound"
            :disabled="cfg.loading.form"
            :loading="cfg.loading.btn"
            :icon="useRenderIcon(btnSaveIcon)"
            @click="save()"
          >
            {{ t("operate.append") }}
          </el-button>
          <el-button
            class="close"
            :round="btnRound"
            :icon="useRenderIcon(btnCloseIcon)"
            @click="close()"
          >
            {{ t("operate.close") }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="css" scoped>
:deep(.member-type-button .el-radio-button__inner) {
  width: 120px;
}

:deep(.member-type-button.is-active .el-radio-button__inner) {
  background-color: #2d85dc;
  border-color: #2d85dc;
  box-shadow: -1px 0 0 0 #2d85dc;
}
</style>
