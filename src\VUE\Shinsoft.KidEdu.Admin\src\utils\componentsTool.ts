// const fs = require("fs");
// const path = require("path");
import fs from "fs";
import path from "node:path";

export function readFilesWithExtension(directory, extension) {
  // console.log(fs);
  fs.readdir(directory, (err, files) => {
    if (err) {
      alert("路径不对");
    } else {
      const filteredFiles = files.filter(file => {
        return path.extname(file) === extension;
      });
      return filteredFiles;
    }
  });
}

export function readFilesFromFolder(folderPath) {
  const files = fs.readdirSync(folderPath);
  const fileContents = [];

  files.forEach(file => {
    console.log("111", file);
    const filePath = path.join(folderPath, file);
    const stats = fs.statSync(filePath);

    if (stats.isFile()) {
      const content = fs.readFileSync(filePath, "utf-8");
      fileContents.push(content);
    }
  });

  return fileContents;
}

export function getfiles(folderPath) {
  // const fs = require("fs");
  // const path = require("node:path");

  // const folderPath = path.join(__dirname, folderPath); // 修改为你的文件夹路径
  fs.readdir(folderPath, (err, files) => {
    if (err) {
      return console.log("Unable to scan directory: " + err);
    }
    return files.filter(file => path.extname(file) === ".txt");
  });
}

// export default readFilesWithExtension;
