<script setup lang="ts">
import { sysSetupApi } from "@/api/sysSetup";
import { SplitPane } from "@/components/SplitPane";
import I18nTree from "./components/i18nTree.vue";
import I18nCulture from "./components/i18nCulture.vue";
import I18nBaseInfo from "./components/i18nBaseInfo.vue";
import EditI18n from "./dialogs/editI18n.vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useI18n } from "vue-i18n";

const { t } = useI18n();

defineOptions({
  name: "platform:i18n:query"
});

const i18nTreeRef = ref();
const editI18nRef = ref();
const i18nCultureRef = ref();

/**
 * 数据变量
 */
const state = reactive({
  // 数据列表：当前页
  data: []
});

const model = defineModel<any>();

/**
 * 初始化组件(created时调用)
 */
const init = () => {
  getList();
};

/**
 * 获取列表数据（异步）
 */
const getList = async () => {
  return new Promise<void>(resolve => {
    sysSetupApi
      .GetI18nTree()
      .then(res => {
        state.data = res.data;
      })
      .finally(() => {});
  });
};

/**
 * 点击数节点触发
 */
const handleNodeClick = data => {
  model.value = data;
  i18nCultureRef.value.initPage(data.id);
};

/**
 * i18n操作：增、删、改
 */
const handleOprateI18n = (oprateType, data) => {
  if (oprateType === "add") {
    editI18nRef.value.open(null, data.id, getDetailGroup(data.group, data.key), data.enumCategory);
  } else if (oprateType === "edit") {
    editI18nRef.value.open(data.id);
  } else if (oprateType === "delete") {
    deleteI18n(data);
  }
};

/**
 * 删除I18n
 */
const deleteI18n = (row: any) => {
  if (row && row.id) {
    ElMessageBox.confirm(
      `${t("operate.confirm.delete")}<br />${t("operate.confirm.deleteHint")}`,
      `${t("operate.title.deleteConfirm")}`,
      {
        showClose: false,
        closeOnClickModal: false,
        draggable: true,
        dangerouslyUseHTMLString: true,
        confirmButtonText: t("operate.ok"),
        cancelButtonText: t("operate.cancel"),
        type: "warning"
      }
    )
      .then(() => {
        sysSetupApi
          .RemoveI18n(row)
          .then(res => {
            // 仅提示
            if (res.success) {
              refresh(t("operate.message.success"), "success");
            }
          })
          .finally(() => {});
      })
      .catch(() => {
        close();
      });
  }
};

/**
 * 刷新table数据
 */
const refresh = (msg?: string, type?: "success" | "warning" | "error" | "info") => {
  if (msg) {
    getList();

    type = type ?? "info";

    ElMessage({
      message: msg,
      type: type,
      duration: 3 * 1000
    });
  }
};

/**
 * 新增i18n子项的group
 */
const getDetailGroup = (group, key) => {
  if (group) {
    return `${group}.${key}`;
  } else {
    return key;
  }
};

/**
 * 新增i18nCulture
 */
const handleAddI18nCulture = data => {
  i18nCultureRef.value.addRow(data);
};

init();
</script>

<template>
  <div>
    <split-pane :panelLeftWidth="450">
      <template #left>
        <div style="margin-top: 1px">
          <I18n-Tree
            ref="i18nTreeRef"
            v-model:data="state.data"
            @node-Click="handleNodeClick"
            @add-I18n-Culture="handleAddI18nCulture"
            @oprate-I18n="handleOprateI18n"
          />
        </div>
      </template>
      <template #right>
        <div class="right-container">
          <I18n-BaseInfo v-model="model" />
          <I18n-Culture ref="i18nCultureRef" />
        </div>
      </template>
    </split-pane>
    <EditI18n ref="editI18nRef" @refresh="getList" />
  </div>
</template>
<style>
.right-container {
  background-color: #f5f6f8;
  height: 100%;
  overflow: auto;
  padding: 0px 18px;
}
.stationInfo {
  padding-top: 17px;
  padding-left: 15px;
  line-height: 25px;
  height: auto;
  min-height: 70px;
  background-color: white;
  margin-bottom: 15px;
  color: #606266;
  font-size: 13px;
}
.station-title {
  font-weight: bold;
  padding-bottom: 15px;
}
</style>
