import { useHttpApi } from "./libs/httpApi";

const controller = "SysSetup";

const api = useHttpApi(controller);

export const sysSetupApi = {
  QueryCompany(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryCompany", data, config);
  },
  GetCompany(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetCompany", { id }, config);
  },
  AddCompany(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddCompany", data, config);
  },
  UpdateCompany(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateCompany", data, config);
  },
  QueryCompanySetting(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryCompanySetting", data, config);
  },
  GetCompanySetting(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetCompanySetting", { id }, config);
  },
  UpdateCompanySetting(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateCompanySetting", data, config);
  },
  QueryLog(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryLog", data, config);
  },
  GetLog(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("GetLog", data, config);
  },
  GetI18nTree() {
    return api.get<BizResult>("GetI18nTree");
  },
  QueryI18nCulture(data: any, config?: ApiRequestConfig) {
    return api.get<QueryResult>("QueryI18nCulture", data, config);
  },
  RemoveI18nCulture(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("RemoveI18nCulture", data, config);
  },
  GetI18nCulture(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetI18nCulture", { id }, config);
  },
  AddI18nCulture(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddI18nCulture", data, config);
  },
  UpdateI18nCulture(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateI18nCulture", data, config);
  },
  GetI18n(id: string, config?: ApiRequestConfig) {
    return api.get<BizResult>("GetI18n", { id }, config);
  },
  AddI18n(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("AddI18n", data, config);
  },
  UpdateI18n(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("UpdateI18n", data, config);
  },
  RemoveI18n(data: any, config?: ApiRequestConfig) {
    return api.post<BizResult>("RemoveI18n", data, config);
  }
};
