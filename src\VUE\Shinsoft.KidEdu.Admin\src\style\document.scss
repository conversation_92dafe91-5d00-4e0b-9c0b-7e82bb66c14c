
.main-page-content{
  
    width: calc(100% - 20px);
    margin: 0 auto;
    margin-bottom: 20px;
    .card-title{
      display: flex;
      flex-direction: row;
      width: 100%;
      justify-content: space-between;
      font-size: 1.4rem;
      div{
        display: flex;
        flex-direction: row;
      }
      div >*{
        margin-right: 10px;
      }
    }
    .btn-content{
      display: flex;
      flex-direction: column;
      justify-content: center;
      height: 60px;
      margin-bottom: 0px;
      background-color: white;
      border-bottom: 1px solid #e0e0e0;
      padding: 0px 10px;
      box-shadow: 0px 4px 10px #e0e0e0;
      
    }
    
    .app-title{
      font-size: 2.2em;
      color: #3b3b3b;
      padding: .5em;
      // padding-top: .7em;
    }
    .btn-align{
      display: flex;
      flex-direction: row;
      width: 100%;
  
      .btn-left{
        text-align: left;
        flex: 1;
      }
      .btn-right{
        text-align: right;
        flex: 1;
      }
    }
  
    .red-start::before{
      color: red;
      vertical-align: text-bottom;
      content:'*';
      display: inline-block;
      height: 20px;
      margin-right: 4px;
      margin-top: 5px;
  
    }
  }
  .components-list{
    margin-bottom: 15px;
  }
/**印章样式**/
  .seal-com {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    margin: 0px 5px;
    .job-title {
      text-align: center;
      padding: 0px 5px;
    }
    .seal-border {
      border-radius: 50%;
      border-width: 5px;
      display: flex;
      flex-direction: column;
  
      div {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border-bottom-width: 5px;
        border-color: inherit;
        color: inherit;
        min-height: 30%;
      }
      div:first-child {
        max-height: 42%;
        min-height: 40%;
        line-height: 120%;
      }
      div:last-child {
        border: 0px;
      }
    }
  }