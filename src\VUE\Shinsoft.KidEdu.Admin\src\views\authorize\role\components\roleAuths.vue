<script setup lang="ts">
import { authorizeApi } from "@/api/authorize";
import { TreeNodeData } from "element-plus/es/components/tree/src/tree.type.mjs";
import Node from "element-plus/es/components/tree/src/model/node";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const tt = t;

defineOptions({
  name: "role:view:auths"
});

/**
 * 定义属性
 */
const props = defineProps({
  // form：字段间隔
  formGutter: {
    type: Number,
    default: 5
  },
  // form：字段标题：宽度
  formLabelWidth: {
    type: String,
    default: "150px"
  },
  // form：字段内容：宽度
  formColSpan: {
    type: Number,
    default: 24
  }
});

const roleId = defineModel<string>("roleId");

const customNodeClass = (data: TreeNodeData, node: Node) => {
  if (data.enumType === 1) {
    return "auth-permission";
  } else if (data.children?.some(auth => auth.enumType === 1) === true) {
    return "auth-group";
  }
  return null;
};

/**
 * 基本配置定义
 */
const cfg = reactive({
  // 默认数据
  default: {
    auths: [],
    tagAuths: [],
    tabsIndex: "auths"
  },
  tree: {
    props: {
      label: "name",
      class: customNodeClass
    }
  },
  // loading：控制变量
  loading: {
    tabs: false
  }
});

/**
 * 数据变量
 */
const state = reactive({
  auths: cfg.default.auths,
  tagAuths: cfg.default.tagAuths,
  tabsIndex: cfg.default.tabsIndex
});

/**
 * 初始化组件
 */
const init = (init?: boolean) => {
  init ??= true;
  if (init) {
    // 防止之前打开的数据残留，因此初始化时赋予state默认值
    initState();
    get();
  }
};

/**
 * 获取model数据
 */
const get = () => {
  if (roleId.value) {
    cfg.loading.tabs = true;
    authorizeApi
      .GetRoleAuths(roleId.value, false)
      .then(res => {
        if (res.success) {
          state.auths = res.data.auths;
          state.tagAuths = res.data.tagAuths;
        }
      })
      .finally(() => {
        cfg.loading.tabs = false;
      });
  }
};

/**
 * 初始化state
 */
const initState = () => {
  state.auths = cfg.default.auths;
  state.tagAuths = cfg.default.tagAuths;
  state.tabsIndex = cfg.default.tabsIndex;
};

/**
 * 对外开放的公共方法
 */
defineExpose({
  init
});

const name = "name";
</script>

<template>
  <div>
    <el-tabs v-model="state.tabsIndex" v-loading="cfg.loading.tabs" tab-position="top">
      <el-tab-pane :label="t('role.tab.baseAuth')" name="auths">
        <el-tree
          style="max-width: 90%"
          :data="state.auths"
          node-key="id"
          default-expand-all
          :expand-on-click-node="true"
          :props="cfg.tree.props"
        />
      </el-tab-pane>
      <el-tab-pane v-if="state.tagAuths?.length > 0" :label="t('role.tab.tagAuth')" name="tagAuths">
        <el-form
          ref="formRef"
          label-position="right"
          :label-width="formLabelWidth"
          class="el-dialog-form"
        >
          <el-row v-for="tagAuth in state.tagAuths" :key="tagAuth.authId" :gutter="formGutter">
            <el-col :span="24">
              <el-form-item :label="tagAuth.authName">
                <el-row v-if="tagAuth.allowAllTags" style="width: 100%">
                  <el-col :span="24">
                    <span style="font-size: 13px; color: #333">{{ t("role.allTags") }}</span>
                  </el-col>
                </el-row>
                <el-row v-else style="width: 100%">
                  <el-col v-for="authTag in tagAuth.authTags" :key="authTag.id" :span="6">
                    {{ authTag.name }}
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="css" scoped>
:deep(.auth-permission .el-tree-node__content) {
  color: #626aef;
}

:deep(.auth-group .el-tree-node__children) {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  display: flex;
  flex-wrap: wrap;
  position: relative;
}

:deep(.auth-permission) {
  flex-grow: 0;
  flex-shrink: 0;
  flex-basis: 25%;
}
</style>
