<script setup lang="ts">
import { goToRoute } from "@/router/utils";
import { useI18n } from "vue-i18n";
import { ElMessage } from "element-plus";
const { t } = useI18n();
const tt = t;

var mainPageRef = ref();

function addDialog() {
  // mainPageRef.value.open();
  const route = {
    name: "mainPage",
    path: "/mainPage",
    query: { id: "123221" }
  };

  goToRoute(route, null, "查看详情");
}

function editDialog() {
  // mainPageRef.value.open("123");
  const route = {
    name: "mainPage",
    path: "/mainPage",
    query: { id: "1231" }
  };

  goToRoute(route, null, "编辑信息");
}
// onMounted(() => {
//   console.log("ccccccccc");
// });
defineOptions({
  name: "example:query"
});
</script>

<template>
  <div>
    <el-button class="query" title="新增" @click="addDialog">新增</el-button>
    <el-button class="query" title="新增" @click="editDialog">编辑</el-button>
  </div>
</template>
