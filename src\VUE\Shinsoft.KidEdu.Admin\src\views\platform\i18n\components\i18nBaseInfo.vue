<script setup lang="ts">
import { OfficeBuilding } from "@element-plus/icons-vue";

defineOptions({
  name: "i18n:baseInfo"
});

/**
 * 基本配置定义
 */
const cfg = reactive({
  baseInfo: {
    titleSpan: 2,
    contentSpan: 6
  }
});

/**
 * 数据变量
 */
const model = defineModel<Record<string, any>>("modelValue", {
  required: true,
  default: {}
});
</script>

<template>
  <div>
    <el-row class="stationInfo">
      <el-col class="station-title">
        <el-icon size="15" span="24"><OfficeBuilding /></el-icon>基础信息
      </el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">Key值：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{ model.key }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">名称：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{ model.name }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">分组：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{ model.group }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">标签：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{
        model.enumFlagsDesc
      }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">分类：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{
        model.enumCategoryDesc
      }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">类型：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{
        model.enumTypeDesc
      }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">有效性：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{
        model.valid ? "是" : model.valid === false ? "否" : ""
      }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">是否系统：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{
        model.isSys ? "是" : model.isSys === false ? "否" : ""
      }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">默认值：</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.contentSpan">{{ model.text }}</el-col>
      <el-col class="product-content" :span="cfg.baseInfo.titleSpan">备注：</el-col>
      <el-col class="product-content" :span="22">{{ model.remark }}</el-col>
    </el-row>
  </div>
</template>
